# Setup test environment for Java deployment pipeline
# This script creates a mock environment for testing

param(
    [string]$TestDir = ".\pipeline-test-env"
)

Write-Host "=== Setting up Pipeline Test Environment ===" -ForegroundColor Green

# Create test directory structure
$testDirs = @(
    "$TestDir\java-download",
    "$TestDir\java-staging", 
    "$TestDir\mock-artifacts",
    "$TestDir\C\Java",
    "$TestDir\C\Java\backup"
)

foreach ($dir in $testDirs) {
    New-Item -ItemType Directory -Force -Path $dir | Out-Null
    Write-Host "✓ Created directory: $dir" -ForegroundColor Green
}

# Create a mock Java JDK structure (for testing)
$mockJdkPath = "$TestDir\mock-artifacts\openjdk-17"
New-Item -ItemType Directory -Force -Path "$mockJdkPath\bin" | Out-Null
New-Item -ItemType Directory -Force -Path "$mockJdkPath\lib" | Out-Null

# Create mock java.exe (batch file for testing)
$mockJavaExe = @"
@echo off
echo openjdk version "17.0.2" 2022-01-18
echo OpenJDK Runtime Environment (build 17.0.2+8-Ubuntu-120.04)
echo OpenJDK 64-Bit Server VM (build 17.0.2+8-Ubuntu-120.04, mixed mode, sharing)
"@

$mockJavaExe | Out-File -FilePath "$mockJdkPath\bin\java.exe" -Encoding ASCII
Write-Host "✓ Created mock java.exe" -ForegroundColor Green

# Create a zip file of the mock JDK
$zipPath = "$TestDir\mock-artifacts\openjdk-17.zip"
try {
    Compress-Archive -Path $mockJdkPath -DestinationPath $zipPath -Force
    Write-Host "✓ Created mock Java package: $zipPath" -ForegroundColor Green
} catch {
    Write-Warning "Failed to create zip file: $_"
}

# Create test configuration file
$testConfig = @"
# Test Configuration for Java Deployment Pipeline

## Test Environment Paths
- Test Directory: $TestDir
- Mock Java Package: $zipPath
- Mock Install Path: $TestDir\C\Java

## Required Pipeline Variables (for actual deployment)
Set these in your Azure DevOps pipeline:

### Required Variables:
- WindowsServerName: 'your-windows-server'
- ArtifactsFeedName: 'your-artifacts-feed'
- WindowsServerConnection: 'your-service-connection'

### Optional Variables:
- JAVA_VERSION: '17' (default)
- JAVA_INSTALL_PATH: 'C:\Java' (default)

## Testing Steps:
1. Run validate-pipeline.ps1 to check YAML syntax
2. Run test-java-deployment.ps1 with test parameters
3. Set up actual Azure DevOps environment
4. Test with real Azure Artifacts feed

## Azure DevOps Setup Checklist:
□ Create Azure Artifacts feed
□ Upload Java JDK zip to artifacts feed
□ Create service connection to Windows server
□ Create 'windows-servers' environment
□ Register Windows server as environment resource
□ Set pipeline variables
□ Test pipeline with manual trigger
"@

$testConfig | Out-File -FilePath "$TestDir\TEST-README.md" -Encoding UTF8
Write-Host "✓ Created test configuration: $TestDir\TEST-README.md" -ForegroundColor Green

# Create a simple test runner script
$testRunner = @"
# Test Runner for Java Deployment Pipeline
# Run this script to execute all tests

Write-Host "=== Running Pipeline Tests ===" -ForegroundColor Green

# Test 1: Validate pipeline YAML
Write-Host "`n1. Validating pipeline YAML..." -ForegroundColor Cyan
.\validate-pipeline.ps1

# Test 2: Test Java deployment scripts
Write-Host "`n2. Testing Java deployment scripts..." -ForegroundColor Cyan
.\test-java-deployment.ps1 -JavaInstallPath "$TestDir\C\Java" -TestJavaPackagePath "$TestDir\mock-artifacts"

Write-Host "`n=== All Tests Complete ===" -ForegroundColor Green
Write-Host "Check the output above for any errors or warnings."
Write-Host "Next step: Set up actual Azure DevOps environment using TEST-README.md"
"@

$testRunner | Out-File -FilePath "$TestDir\run-tests.ps1" -Encoding UTF8
Write-Host "✓ Created test runner: $TestDir\run-tests.ps1" -ForegroundColor Green

Write-Host "`n=== Test Environment Setup Complete ===" -ForegroundColor Green
Write-Host "Test directory created at: $TestDir"
Write-Host "`nNext steps:"
Write-Host "1. cd $TestDir"
Write-Host "2. .\run-tests.ps1"
Write-Host "3. Review TEST-README.md for Azure DevOps setup"
