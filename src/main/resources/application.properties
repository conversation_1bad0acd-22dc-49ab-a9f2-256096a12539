spring.application.name=demo

# Server configuration
server.port=8081

# H2 Database configuration
spring.datasource.url=jdbc:h2:mem:testdb
spring.datasource.driverClassName=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=password
spring.h2.console.enabled=true
spring.h2.console.path=/h2-console

# JPA configuration
spring.jpa.database-platform=org.hibernate.dialect.H2Dialect
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.show-sql=true

# JSF Configuration (disabled for now)
# joinfaces.faces.project-stage=development
# joinfaces.faces.facelets.skip-comments=true
# joinfaces.enabled=false
