# Quick Windows VM Setup for Pipeline Testing

## 🚀 **Super Quick Setup (5 minutes)**

### Step 1: Download the Setup Script
Copy `setup-windows-vm-server.ps1` to your Windows VM

### Step 2: Run the Setup Script
```powershell
# In Windows VM, open PowerShell as Administrator and run:
Set-ExecutionPolicy Bypass -Scope Process -Force
.\setup-windows-vm-server.ps1
```

### Step 3: Validate Setup
```powershell
# Run the validation script created by setup:
PowerShell -File C:\validate-pipeline-environment.ps1
```

### Step 4: Configure Azure DevOps Agent
```powershell
# Navigate to agent directory and configure:
cd C:\PipelineAgent
.\config.cmd

# You'll need:
# - Azure DevOps URL: https://dev.azure.com/YourOrganization
# - Personal Access Token (create in Azure DevOps)
# - Agent Pool: Default
# - Agent Name: WindowsVM-Agent
```

## 📋 **Manual Steps (if script fails)**

### 1. **Basic Setup**
```powershell
# Run as Administrator
Set-ExecutionPolicy RemoteSigned -Force
New-Item -ItemType Directory -Path "C:\Java" -Force
New-Item -ItemType Directory -Path "C:\Tomcat" -Force
```

### 2. **Create Admin User**
```powershell
net user pipelineadmin Pipeline123! /add
net localgroup administrators pipelineadmin /add
```

### 3. **Open Firewall Ports**
```powershell
New-NetFirewallRule -DisplayName "Allow Port 8080" -Direction Inbound -Protocol TCP -LocalPort 8080 -Action Allow
```

## 🔧 **Parallels VM Settings**

### Recommended Configuration:
- **RAM**: 4GB minimum (8GB preferred)
- **CPU**: 2-4 cores
- **Disk**: 60GB minimum
- **Network**: Shared Network
- **Graphics**: 512MB minimum

### Performance Optimization:
```bash
# In Parallels Desktop:
1. VM Settings > Hardware > CPU & Memory
   - Enable "Hypervisor" if available
   - Allocate 4GB+ RAM
   
2. VM Settings > Hardware > Graphics
   - Enable "3D acceleration"
   - Allocate 512MB+ video memory

3. VM Settings > Options > Optimization
   - Set to "Faster virtual machine"
```

## 🌐 **Network Setup**

### Find Your VM's IP Address:
```powershell
# In Windows VM:
Get-NetIPAddress -AddressFamily IPv4 | Where-Object {$_.IPAddress -notlike "127.*"}
```

### Test Network Connectivity:
```powershell
# Test internet access:
Test-NetConnection -ComputerName google.com -Port 80

# Test from Mac to VM (replace with your VM's IP):
ping ***********
```

## 🎯 **Azure DevOps Integration**

### 1. **Create Environment**
```yaml
# In Azure DevOps:
Pipelines > Environments > New Environment
Name: "windows-servers"
Resource Type: "Virtual machines"
```

### 2. **Add VM as Resource**
```yaml
# In the environment:
Add resource > Virtual machine
Operating system: Windows
Copy the registration script and run it on your VM
```

### 3. **Test Connection**
```yaml
# Create a simple test pipeline:
trigger: none
pool:
  vmImage: 'windows-latest'
stages:
- stage: Test
  jobs:
  - deployment: TestVM
    environment: 'windows-servers'
    strategy:
      runOnce:
        deploy:
          steps:
          - script: echo "Hello from Windows VM!"
```

## 🐛 **Common Issues & Solutions**

### Issue: "Access Denied" errors
```powershell
# Solution: Run PowerShell as Administrator
# Right-click PowerShell > "Run as Administrator"
```

### Issue: Agent won't connect
```powershell
# Check firewall and network:
Test-NetConnection -ComputerName dev.azure.com -Port 443
```

### Issue: Pipeline can't create directories
```powershell
# Fix permissions:
icacls "C:\Java" /grant "Everyone:(OI)(CI)F" /T
icacls "C:\Tomcat" /grant "Everyone:(OI)(CI)F" /T
```

### Issue: Environment variables not set
```powershell
# Check if running as admin:
([Security.Principal.WindowsPrincipal][Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
```

## ✅ **Verification Checklist**

```bash
□ Windows VM has 4GB+ RAM
□ PowerShell execution policy set
□ C:\Java and C:\Tomcat directories exist
□ Admin user created (pipelineadmin)
□ Firewall ports opened (8080, 5985)
□ Azure DevOps agent installed and running
□ Environment created in Azure DevOps
□ VM registered as environment resource
□ Test pipeline runs successfully
```

## 🎉 **You're Ready!**

Once all steps are complete, your Windows VM will act like a Windows Server for testing your Java + Tomcat deployment pipeline.

**Next**: Try running your `azure-pipelines-robust.yml` pipeline with these parameters:
- `deployJava: true`
- `deployTomcat: false` (start simple)
- `skipBackup: true` (for first test)

Good luck! 🚀
