# Java & Tomcat Deployment Pipeline

This repository contains Azure DevOps pipeline for deploying Java JDK and Apache Tomcat from Azure Artifacts feed to Windows servers.

## 🎯 Overview

The pipeline downloads Java and Tomcat packages from your Azure Artifacts feed and deploys them to target Windows servers with comprehensive testing and validation.

## 📋 Prerequisites

1. **Azure Artifacts Feed**: `java-tomcat-packages` feed in your Azure DevOps project
2. **Packages in Feed**: 
   - `java-jdk` version `17.0.0`
   - `apache-tomcat` version `10.1.15`
3. **Azure DevOps Environment**: `windows-servers` environment configured
4. **Target Server**: Windows server with PowerShell 5.1+

## 🚀 Quick Start

### Step 1: Upload Packages to Azure Artifacts

First, upload the required packages to your feed:

```powershell
# Run the upload script (requires Personal Access Token)
.\upload-packages-to-feed.ps1 -PersonalAccessToken "your_token_here"
```

**Get Personal Access Token:**
1. Go to Azure DevOps → Profile → Personal Access Tokens
2. Create new token with **"Packaging (read, write, & manage)"** permissions
3. Copy the token and use it in the script

### Step 2: Configure Pipeline

1. **Create Pipeline**: 
   - Go to Azure DevOps → Pipelines → New Pipeline
   - Choose your repository
   - Select existing YAML file: `azure-pipelines.yml`

2. **Configure Environment**:
   - Go to Environments → Create `windows-servers`
   - Add your target Windows servers

3. **Run Pipeline**:
   - Click "Run pipeline"
   - Monitor the deployment progress

## 📁 Files Description

| File | Description |
|------|-------------|
| `azure-pipelines.yml` | Main deployment pipeline with comprehensive testing |
| `upload-packages-to-feed.ps1` | Script to upload Java and Tomcat to Azure Artifacts |
| `test-deployment.ps1` | Standalone comprehensive test script |
| `azure-pipelines-direct-download.yml` | Alternative pipeline (downloads from internet) |
| `azure-pipelines-simple.yml` | Simplified version for testing |

## 🧪 Testing

The pipeline includes comprehensive testing:

### Automated Tests (Built into Pipeline)
- ✅ Java installation verification
- ✅ Environment variables validation
- ✅ Tomcat installation verification
- ✅ Process and service checks
- ✅ HTTP connectivity tests
- ✅ Performance and load testing

### Manual Testing
Run the standalone test script:

```powershell
.\test-deployment.ps1 -JavaHomePath "C:\Java\jdk-17" -TomcatHomePath "C:\Tomcat\apache-tomcat-10.1"
```

## 🔧 Pipeline Stages

### Stage 1: Check Versions and Download
- Downloads Java and Tomcat from Azure Artifacts
- Extracts and prepares packages
- Publishes artifacts for deployment

### Stage 2: Deploy Java and Tomcat
- Stops existing Tomcat services
- Backs up current installations
- Installs new Java and Tomcat versions
- Updates environment variables
- Configures Tomcat with Java integration
- Starts Tomcat service

### Stage 3: Comprehensive Testing
- Validates all installations
- Tests functionality and performance
- Generates detailed test reports

## 📊 What Gets Installed

### Java JDK 17
- **Location**: `C:\Java\jdk-17`
- **Environment**: `JAVA_HOME` set to installation path
- **PATH**: Java bin directory added to system PATH

### Apache Tomcat 10.1.15
- **Location**: `C:\Tomcat\apache-tomcat-10.1.15`
- **Environment**: `CATALINA_HOME` set to installation path
- **Configuration**: `setenv.bat` created with Java integration
- **Service**: Tomcat started and accessible on port 8080

## 🔍 Troubleshooting

### Common Issues

**1. Feed Access Issues**
```
Error: Package not found in feed
```
**Solution**: Ensure packages are uploaded and pipeline has feed access permissions.

**2. Environment Variables Not Set**
```
Error: JAVA_HOME not found
```
**Solution**: Pipeline sets machine-level variables. Restart services or reboot server.

**3. Tomcat Won't Start**
```
Error: Tomcat process not found
```
**Solution**: Check Java installation and setenv.bat configuration.

### Verification Commands

```powershell
# Check Java
java -version
echo $env:JAVA_HOME

# Check Tomcat
Get-Process -Name "*java*" | Where-Object { $_.CommandLine -like "*catalina*" }
Invoke-WebRequest -Uri "http://localhost:8080"
```

## 🎛️ Configuration

### Pipeline Variables
- `JAVA_VERSION`: Java version (default: 17)
- `TOMCAT_VERSION`: Tomcat version (default: 10.1)
- `JAVA_INSTALL_PATH`: Java installation directory
- `TOMCAT_INSTALL_PATH`: Tomcat installation directory

### Feed Configuration
- **Organization**: `brianmukandiwa0811`
- **Project**: `pipe`
- **Feed**: `java-tomcat-packages`

## 📈 Monitoring

The pipeline provides detailed logging and test results:
- Installation progress tracking
- Comprehensive test suite results
- Performance metrics
- Error details and troubleshooting hints

## 🔄 Updates

To update Java or Tomcat:
1. Upload new package versions to Azure Artifacts
2. Update version numbers in pipeline variables
3. Run the pipeline

The pipeline automatically backs up existing installations before updating.

## 🆘 Support

If you encounter issues:
1. Check the pipeline logs for detailed error messages
2. Run the standalone test script for diagnosis
3. Verify Azure Artifacts feed access and package availability
4. Ensure target servers meet prerequisites

---

**Ready to deploy!** 🚀 Run the upload script first, then execute the pipeline.
