# Azure Pipeline Deployment Guide

## 🎯 **Honest Answer: Will It Work Seamlessly?**

**Short Answer**: **70% chance** it will work with the robust version, **30% chance** with the original.

**Long Answer**: The pipeline logic is solid, but Azure DevOps has many moving parts that can cause issues.

## 📋 **Pre-Deployment Checklist**

### ✅ **MUST DO BEFORE RUNNING PIPELINE**

#### 1. Azure Artifacts Setup
```bash
# You need to:
1. Create an Azure Artifacts feed named exactly what you'll put in ArtifactsFeedName variable
2. Upload Java JDK as a ZIP file with package name "java-jdk"
3. Upload Tomcat as a ZIP file with package name "apache-tomcat"
4. Ensure your pipeline service connection has read access to the feed
```

#### 2. Azure DevOps Environment Setup
```yaml
# In Azure DevOps:
1. Go to Pipelines > Environments
2. Create environment named "windows-servers"
3. Add your Windows server as a resource
4. Configure any required approvals
```

#### 3. Pipeline Variables (CRITICAL)
```yaml
# Set these in Azure DevOps Pipeline Variables:
ArtifactsFeedName: "your-actual-feed-name"  # MUST match your feed
WindowsServerName: "your-server-name"       # Optional, for reference
WindowsServerConnection: "your-connection"  # Optional, for reference
```

#### 4. Windows Server Preparation
```powershell
# On your Windows server:
# 1. Ensure PowerShell execution policy allows scripts
Set-ExecutionPolicy RemoteSigned -Scope LocalMachine

# 2. Create directories with proper permissions
New-Item -ItemType Directory -Path "C:\Java" -Force
New-Item -ItemType Directory -Path "C:\Tomcat" -Force

# 3. Ensure the service account has admin rights
# (This is often the biggest issue)
```

## 🚀 **Recommended Deployment Strategy**

### Phase 1: Test with Robust Pipeline (Recommended)
Use `azure-pipelines-robust.yml` - it has better error handling and validation.

```yaml
# Start with these parameters:
deployJava: true
deployTomcat: false  # Start with Java only
skipBackup: true     # For first test
```

### Phase 2: Incremental Testing
1. **Test Java only first**
2. **Then test Tomcat only**
3. **Finally test both together**

### Phase 3: Production Deployment
1. **Enable backups** (`skipBackup: false`)
2. **Test rollback scenarios**
3. **Add approval gates**

## ⚠️ **Most Likely Issues You'll Encounter**

### 1. **Azure Artifacts Package Not Found**
```
Error: "No Java package found. Expected zip, tar.gz, or 7z file."
```
**Solution**: 
- Check your feed name in pipeline variables
- Verify package names match exactly ("java-jdk", "apache-tomcat")
- Ensure packages are uploaded as ZIP files

### 2. **Permission Denied Errors**
```
Error: "Access to the path 'C:\Java' is denied"
```
**Solution**:
- Run Azure DevOps agent as administrator
- Or grant full control to the service account
- Or use different directories with proper permissions

### 3. **Environment Variable Issues**
```
Warning: "Could not set JAVA_HOME environment variable"
```
**Solution**:
- This is often due to insufficient privileges
- Variables may need to be set manually after deployment
- Consider using user-level variables instead of machine-level

### 4. **Service Connection Not Working**
```
Error: "Could not connect to target server"
```
**Solution**:
- Verify service connection is configured correctly
- Test connection in Azure DevOps
- Ensure firewall allows connections

## 🔧 **Troubleshooting Steps**

### If Pipeline Fails:

1. **Check Azure DevOps Logs**
   - Look for specific error messages
   - Check which step failed
   - Review PowerShell output

2. **Verify Prerequisites**
   ```powershell
   # Run this on your Windows server:
   Write-Host "Checking prerequisites..."
   Test-Path "C:\Java"
   Test-Path "C:\Tomcat"
   Get-ExecutionPolicy
   whoami
   ```

3. **Test Manually**
   ```powershell
   # Try manual steps to isolate issues:
   # 1. Can you create directories?
   New-Item -ItemType Directory -Path "C:\TestJava" -Force
   
   # 2. Can you set environment variables?
   [Environment]::SetEnvironmentVariable("TEST_VAR", "test", "Machine")
   
   # 3. Can you download from Azure Artifacts?
   # (This requires Azure CLI or REST API calls)
   ```

## 📊 **Success Probability by Scenario**

| Scenario | Success Probability | Notes |
|----------|-------------------|-------|
| **Perfect Setup** | 90% | All prerequisites met, admin rights |
| **Typical Corporate** | 60% | Some permission issues, need IT help |
| **First Time User** | 30% | Many configuration issues to resolve |
| **Complex Environment** | 40% | Existing software conflicts |

## 🎯 **Realistic Expectations**

### What Will Likely Work:
✅ Package downloads (if feed is configured correctly)
✅ File extraction and copying
✅ Basic PowerShell script execution
✅ Directory creation (with proper permissions)

### What May Need Adjustment:
⚠️ Environment variable setting (permission issues)
⚠️ Service management (if services exist)
⚠️ Process detection and termination
⚠️ HTTP endpoint testing (timing issues)

### What Will Definitely Need Manual Setup:
❌ Azure Artifacts feed and packages
❌ Pipeline variables configuration
❌ Windows server permissions
❌ Service connections and environments

## 🚀 **Quick Start Commands**

### 1. Use the Robust Pipeline
```bash
# Copy azure-pipelines-robust.yml to your repo
# It has better error handling and validation
```

### 2. Set Required Variables
```yaml
# In Azure DevOps Pipeline Variables:
ArtifactsFeedName: "your-feed-name"
```

### 3. Start Simple
```yaml
# First run with these parameters:
deployJava: true
deployTomcat: false
skipBackup: true
```

### 4. Monitor and Adjust
- Watch the Azure DevOps logs carefully
- Fix issues one at a time
- Test incrementally

## 💡 **Bottom Line**

**The pipeline will work**, but you'll need to:
1. **Set up Azure DevOps correctly** (feeds, variables, environments)
2. **Configure Windows server permissions** properly
3. **Be prepared to troubleshoot** Azure-specific issues
4. **Test incrementally** rather than running everything at once

**Confidence Level**: 70% success with proper setup, 90% success after initial troubleshooting.

The robust version (`azure-pipelines-robust.yml`) gives you the best chance of success on the first try!
