# Complete Azure Pipeline Setup Guide

## 🎯 **Overview: What We're Going to Do**

1. ✅ **Windows VM** - Already done! (Network working)
2. 🔄 **Azure DevOps Project** - Set up organization and project
3. 🔄 **Azure Artifacts** - Create feed and upload Java/Tomcat
4. 🔄 **Environment Setup** - Register your VM
5. 🔄 **Pipeline Creation** - Deploy your pipeline
6. 🔄 **Test & Deploy** - Run your first deployment

---

## 📋 **Step 1: Azure DevOps Project Setup (5 minutes)**

### **1.1 Create Azure DevOps Account (if you don't have one)**
1. Go to https://dev.azure.com
2. Sign in with Microsoft account (or create one)
3. Click "Create organization"
4. Choose organization name (e.g., "YourName-DevOps")

### **1.2 Create Project**
1. Click "New Project"
2. Project name: `java-tomcat-deployment`
3. Visibility: Private
4. Click "Create"

### **1.3 Get Your Organization URL**
```
Your URL will be: https://dev.azure.com/YourOrgName
Save this - you'll need it later!
```

---

## 📦 **Step 2: Azure Artifacts Setup (10 minutes)**

### **2.1 Create Artifacts Feed**
1. In Azure DevOps, go to **Artifacts** (left menu)
2. Click **"Create Feed"**
3. Feed name: `java-tomcat-packages`
4. Visibility: Private
5. Click **"Create"**

### **2.2 Download Java JDK**
```bash
# Download OpenJDK 17 (or your preferred version)
# Go to: https://adoptium.net/temurin/releases/
# Download: OpenJDK 17 (LTS) - Windows x64 ZIP
# Save as: openjdk-17-windows.zip
```

### **2.3 Download Apache Tomcat**
```bash
# Go to: https://tomcat.apache.org/download-10.cgi
# Download: Core - Windows Service Installer OR zip
# Save as: apache-tomcat-10.1.zip
```

### **2.4 Upload Packages to Artifacts**

**Upload Java:**
1. In your feed, click **"Upload package"**
2. Package type: **Universal**
3. Package name: `java-jdk`
4. Version: `17.0.0` (or current version)
5. Upload your `openjdk-17-windows.zip`
6. Click **"Upload"**

**Upload Tomcat:**
1. Click **"Upload package"** again
2. Package type: **Universal**
3. Package name: `apache-tomcat`
4. Version: `10.1.0` (or current version)
5. Upload your `apache-tomcat-10.1.zip`
6. Click **"Upload"**

---

## 🖥️ **Step 3: Environment Setup (5 minutes)**

### **3.1 Create Environment**
1. Go to **Pipelines** → **Environments**
2. Click **"Create environment"**
3. Name: `windows-servers`
4. Description: `Windows VM for Java/Tomcat deployment`
5. Click **"Create"**

### **3.2 Add Your VM as Resource**
1. In the `windows-servers` environment, click **"Add resource"**
2. Select **"Virtual machines"**
3. Operating system: **Windows**
4. Copy the registration script shown
5. **Run this script on your Windows VM** (as Administrator)

**Alternative Manual Registration:**
If the script doesn't work, manually register:
1. Download agent from the environment page
2. Extract to `C:\PipelineAgent`
3. Run `.\config.cmd` and provide:
   - Server URL: `https://dev.azure.com/YourOrgName`
   - Authentication: PAT (Personal Access Token)
   - Agent pool: Default
   - Agent name: `WindowsVM-Agent`

---

## 🔑 **Step 4: Create Personal Access Token (3 minutes)**

### **4.1 Generate PAT**
1. Click your profile picture (top right)
2. Select **"Personal access tokens"**
3. Click **"New Token"**
4. Name: `Pipeline-Agent-Token`
5. Expiration: 90 days (or custom)
6. Scopes: **Full access** (or select specific scopes)
7. Click **"Create"**
8. **COPY THE TOKEN** - you won't see it again!

---

## 🚀 **Step 5: Pipeline Creation (10 minutes)**

### **5.1 Create Pipeline Variables**
1. Go to **Pipelines** → **Library**
2. Click **"Variable group"**
3. Name: `java-tomcat-config`
4. Add variables:
   ```
   ArtifactsFeedName: java-tomcat-packages
   WindowsServerName: ***********
   WindowsServerConnection: windows-vm-connection
   ```
5. Click **"Save"**

### **5.2 Create Your Pipeline**
1. Go to **Pipelines** → **Pipelines**
2. Click **"Create Pipeline"**
3. Select **"Azure Repos Git"** (or your repo location)
4. Select your repository
5. Choose **"Existing Azure Pipelines YAML file"**
6. Select your `azure-pipelines-robust.yml` file
7. Click **"Continue"**

### **5.3 Link Variable Group**
1. In pipeline editor, click **"Variables"**
2. Click **"Variable groups"**
3. Link the `java-tomcat-config` group
4. Click **"Save"**

---

## 🧪 **Step 6: Test Deployment (15 minutes)**

### **6.1 First Test - Simple Connection**
Create a simple test pipeline first:

```yaml
trigger: none

pool:
  vmImage: 'windows-latest'

stages:
- stage: TestConnection
  jobs:
  - deployment: TestVM
    environment: 'windows-servers'
    strategy:
      runOnce:
        deploy:
          steps:
          - script: |
              echo "=== Connection Test ==="
              echo "Computer: %COMPUTERNAME%"
              echo "Current Directory: %CD%"
              dir C:\
              echo "Java Directory: %CD%"
              dir C:\Java
              echo "=== Test Complete ==="
            displayName: 'Test Connection'
```

### **6.2 Run Test Pipeline**
1. Click **"Run pipeline"**
2. Select **"Run"**
3. Watch the logs - should show successful connection

### **6.3 Run Java Deployment**
Once test works, run your main pipeline:
1. Use `azure-pipelines-robust.yml`
2. Set parameters:
   - `deployJava: true`
   - `deployTomcat: false` (start simple)
   - `skipBackup: true` (first test)

---

## 📊 **Step 7: Monitor and Troubleshoot**

### **7.1 Check Pipeline Logs**
- Click on running pipeline
- Expand each step to see detailed logs
- Look for error messages in red

### **7.2 Common Issues & Solutions**

**Issue: "Feed not found"**
```
Solution: Check ArtifactsFeedName variable matches your feed name exactly
```

**Issue: "Cannot connect to VM"**
```
Solution: Ensure VM agent is running:
- On VM: Get-Service -Name "vstsagent*"
- Should show "Running" status
```

**Issue: "Access denied"**
```
Solution: Check VM permissions:
- Ensure pipelineadmin user exists
- Check directory permissions on C:\Java and C:\Tomcat
```

**Issue: "Package not found"**
```
Solution: Verify packages in Artifacts:
- Package names must be exactly: "java-jdk" and "apache-tomcat"
- Check package versions are available
```

---

## ✅ **Success Checklist**

```bash
□ Azure DevOps organization created
□ Project created
□ Artifacts feed created with packages uploaded
□ Environment "windows-servers" created
□ VM registered as environment resource
□ Personal Access Token created
□ Pipeline variables configured
□ Test pipeline runs successfully
□ Java deployment works
□ Tomcat deployment works
□ Both Java + Tomcat work together
```

---

## 🎯 **Quick Start Commands**

**On your Windows VM (run as Administrator):**
```powershell
# Verify VM is ready
Get-Service -Name "vstsagent*"
Test-Path "C:\Java"
Test-Path "C:\Tomcat"
whoami
```

**Test from your Mac:**
```bash
# Verify connectivity
ping ***********
nc -zv *********** 8080
```

---

## 🚀 **Next Steps After Setup**

1. **Start Simple**: Deploy Java only first
2. **Add Tomcat**: Once Java works, add Tomcat
3. **Test Integration**: Verify Java + Tomcat work together
4. **Add Monitoring**: Set up logging and health checks
5. **Production Ready**: Add approval gates and notifications

---

**Estimated Total Time: 45-60 minutes**

Each step builds on the previous one, so follow them in order. If you get stuck on any step, we can troubleshoot that specific part!
