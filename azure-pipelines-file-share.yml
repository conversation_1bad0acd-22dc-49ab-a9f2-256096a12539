# Azure Pipeline using File Share or Blob Storage
# Alternative to Azure Artifacts

trigger: none

parameters:
  - name: deployJava
    displayName: 'Deploy Java'
    type: boolean
    default: true
  - name: deployTomcat
    displayName: 'Deploy Tomcat'
    type: boolean
    default: false
  - name: storageMethod
    displayName: 'Storage Method'
    type: string
    default: 'direct'
    values:
    - direct
    - fileshare
    - blob

variables:
  # Storage configuration (update these if using file share or blob)
  STORAGE_ACCOUNT: 'yourstorageaccount'
  FILE_SHARE_NAME: 'packages'
  CONTAINER_NAME: 'packages'
  
  # Installation paths
  JAVA_INSTALL_PATH: 'C:\Java'
  TOMCAT_INSTALL_PATH: 'C:\Tomcat'

pool:
  vmImage: 'windows-latest'

stages:
- stage: Deploy
  jobs:
  - deployment: DeployToServer
    environment: 'windows-servers'
    strategy:
      runOnce:
        deploy:
          steps:
          - task: PowerShell@2
            displayName: 'Deploy Java'
            condition: eq('${{ parameters.deployJava }}', true)
            inputs:
              targetType: 'inline'
              script: |
                Write-Host "=== Installing Java (Method: ${{ parameters.storageMethod }}) ==="
                
                $javaZip = "C:\temp\java.zip"
                New-Item -ItemType Directory -Path "C:\temp" -Force
                
                # Download based on storage method
                switch ("${{ parameters.storageMethod }}") {
                    "direct" {
                        Write-Host "Downloading from GitHub releases..."
                        $javaUrl = "https://github.com/adoptium/temurin17-binaries/releases/download/jdk-17.0.9%2B9/OpenJDK17U-jdk_x64_windows_hotspot_17.0.9_9.zip"
                        Invoke-WebRequest -Uri $javaUrl -OutFile $javaZip -UseBasicParsing
                    }
                    "fileshare" {
                        Write-Host "Downloading from Azure File Share..."
                        # Example: Download from file share
                        # az storage file download --account-name $(STORAGE_ACCOUNT) --share-name $(FILE_SHARE_NAME) --path java/openjdk-17.zip --dest $javaZip
                        Write-Host "Configure your file share download here"
                    }
                    "blob" {
                        Write-Host "Downloading from Azure Blob Storage..."
                        # Example: Download from blob storage
                        # az storage blob download --account-name $(STORAGE_ACCOUNT) --container-name $(CONTAINER_NAME) --name java/openjdk-17.zip --file $javaZip
                        Write-Host "Configure your blob storage download here"
                    }
                }
                
                # Extract and install Java
                Write-Host "Extracting Java..."
                Expand-Archive -Path $javaZip -DestinationPath "C:\temp\java" -Force
                
                $jdkDir = Get-ChildItem -Path "C:\temp\java" -Directory | Select-Object -First 1
                $targetDir = "$(JAVA_INSTALL_PATH)\jdk-17"
                
                New-Item -ItemType Directory -Path "$(JAVA_INSTALL_PATH)" -Force
                if (Test-Path $targetDir) { Remove-Item -Path $targetDir -Recurse -Force }
                
                Copy-Item -Path $jdkDir.FullName -Destination $targetDir -Recurse -Force
                [Environment]::SetEnvironmentVariable("JAVA_HOME", $targetDir, "Machine")
                
                # Verify
                if (Test-Path "$targetDir\bin\java.exe") {
                    Write-Host "✓ Java installed successfully!" -ForegroundColor Green
                } else {
                    Write-Error "Java installation failed"
                }
                
                Remove-Item -Path "C:\temp\java*" -Recurse -Force -ErrorAction SilentlyContinue

          - task: PowerShell@2
            displayName: 'Deploy Tomcat'
            condition: eq('${{ parameters.deployTomcat }}', true)
            inputs:
              targetType: 'inline'
              script: |
                Write-Host "=== Installing Tomcat (Method: ${{ parameters.storageMethod }}) ==="
                
                $tomcatZip = "C:\temp\tomcat.zip"
                New-Item -ItemType Directory -Path "C:\temp" -Force
                
                # Download based on storage method
                switch ("${{ parameters.storageMethod }}") {
                    "direct" {
                        Write-Host "Downloading from Apache archives..."
                        $tomcatUrl = "https://archive.apache.org/dist/tomcat/tomcat-10/v10.1.15/bin/apache-tomcat-10.1.15-windows-x64.zip"
                        Invoke-WebRequest -Uri $tomcatUrl -OutFile $tomcatZip -UseBasicParsing
                    }
                    "fileshare" {
                        Write-Host "Downloading from Azure File Share..."
                        # Configure your file share download
                        Write-Host "Configure your file share download here"
                    }
                    "blob" {
                        Write-Host "Downloading from Azure Blob Storage..."
                        # Configure your blob storage download
                        Write-Host "Configure your blob storage download here"
                    }
                }
                
                # Extract and install Tomcat
                Write-Host "Extracting Tomcat..."
                Expand-Archive -Path $tomcatZip -DestinationPath "C:\temp\tomcat" -Force
                
                $tomcatDir = Get-ChildItem -Path "C:\temp\tomcat" -Directory | Select-Object -First 1
                $targetDir = "$(TOMCAT_INSTALL_PATH)\apache-tomcat-10.1.15"
                
                New-Item -ItemType Directory -Path "$(TOMCAT_INSTALL_PATH)" -Force
                if (Test-Path $targetDir) { Remove-Item -Path $targetDir -Recurse -Force }
                
                Copy-Item -Path $tomcatDir.FullName -Destination $targetDir -Recurse -Force
                [Environment]::SetEnvironmentVariable("CATALINA_HOME", $targetDir, "Machine")
                
                # Verify
                if (Test-Path "$targetDir\bin\catalina.bat") {
                    Write-Host "✓ Tomcat installed successfully!" -ForegroundColor Green
                } else {
                    Write-Error "Tomcat installation failed"
                }
                
                Remove-Item -Path "C:\temp\tomcat*" -Recurse -Force -ErrorAction SilentlyContinue

          - task: PowerShell@2
            displayName: 'Final Summary'
            inputs:
              targetType: 'inline'
              script: |
                Write-Host "=== Deployment Complete ===" -ForegroundColor Green
                Write-Host "Storage method used: ${{ parameters.storageMethod }}"
                Write-Host ""
                
                if ('${{ parameters.deployJava }}' -eq 'true') {
                    Write-Host "Java installed at: $(JAVA_INSTALL_PATH)\jdk-17"
                }
                
                if ('${{ parameters.deployTomcat }}' -eq 'true') {
                    Write-Host "Tomcat installed at: $(TOMCAT_INSTALL_PATH)\apache-tomcat-10.1.15"
                }
                
                Write-Host ""
                Write-Host "Ready to use! 🚀" -ForegroundColor Green
