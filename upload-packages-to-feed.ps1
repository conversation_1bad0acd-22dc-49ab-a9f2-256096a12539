# PowerShell Script to Upload Java and Tomcat Packages to Azure Artifacts Feed
# Run this script to populate your java-tomcat-packages feed

param(
    [Parameter(Mandatory=$true)]
    [string]$PersonalAccessToken,
    
    [Parameter(Mandatory=$false)]
    [string]$Organization = "brianmukandiwa0811",
    
    [Parameter(Mandatory=$false)]
    [string]$Project = "pipe",
    
    [Parameter(Mandatory=$false)]
    [string]$FeedName = "java-tomcat-packages"
)

Write-Host "=== Azure Artifacts Package Upload Script ===" -ForegroundColor Green
Write-Host "Organization: $Organization"
Write-Host "Project: $Project"
Write-Host "Feed: $FeedName"
Write-Host ""

# Create temp directory for downloads
$tempDir = "C:\temp\azure-artifacts-upload"
New-Item -ItemType Directory -Path $tempDir -Force | Out-Null
Write-Host "✓ Created temp directory: $tempDir"

# Function to upload package to Azure Artifacts
function Upload-Package {
    param(
        [string]$PackageName,
        [string]$Version,
        [string]$FilePath,
        [string]$Description
    )
    
    Write-Host "`n=== Uploading $PackageName v$Version ===" -ForegroundColor Yellow
    
    # Create authorization header
    $encodedToken = [Convert]::ToBase64String([Text.Encoding]::ASCII.GetBytes(":$PersonalAccessToken"))
    $headers = @{
        "Authorization" = "Basic $encodedToken"
        "Content-Type" = "application/octet-stream"
    }
    
    # Upload URL
    $uploadUrl = "https://pkgs.dev.azure.com/$Organization/$Project/_apis/packaging/feeds/$FeedName/upack/packages/$PackageName/versions/$Version" + "?api-version=5.1-preview.1"
    
    try {
        Write-Host "Uploading to: $uploadUrl"
        Write-Host "File: $FilePath"
        Write-Host "Size: $([math]::Round((Get-Item $FilePath).Length / 1MB, 1)) MB"
        
        $response = Invoke-RestMethod -Uri $uploadUrl -Method PUT -Headers $headers -InFile $FilePath
        Write-Host "✅ Successfully uploaded $PackageName v$Version" -ForegroundColor Green
        return $true
    }
    catch {
        Write-Host "❌ Failed to upload $PackageName v$Version" -ForegroundColor Red
        Write-Host "Error: $_" -ForegroundColor Red
        return $false
    }
}

# Download and upload Java JDK
Write-Host "`n=== Processing Java JDK ===" -ForegroundColor Cyan

$javaUrl = "https://github.com/adoptium/temurin17-binaries/releases/download/jdk-17.0.9%2B9/OpenJDK17U-jdk_x64_windows_hotspot_17.0.9_9.zip"
$javaFile = Join-Path $tempDir "openjdk-17.zip"

try {
    Write-Host "Downloading Java JDK..."
    Invoke-WebRequest -Uri $javaUrl -OutFile $javaFile -UseBasicParsing
    Write-Host "✓ Java JDK downloaded"
    
    # Upload to Azure Artifacts
    $javaUploaded = Upload-Package -PackageName "java-jdk" -Version "17.0.0" -FilePath $javaFile -Description "OpenJDK 17 for Windows x64"
    
    if ($javaUploaded) {
        Write-Host "✅ Java JDK uploaded successfully" -ForegroundColor Green
    }
}
catch {
    Write-Host "❌ Failed to process Java JDK: $_" -ForegroundColor Red
}

# Download and upload Apache Tomcat
Write-Host "`n=== Processing Apache Tomcat ===" -ForegroundColor Cyan

$tomcatUrl = "https://archive.apache.org/dist/tomcat/tomcat-10/v10.1.15/bin/apache-tomcat-10.1.15-windows-x64.zip"
$tomcatFile = Join-Path $tempDir "apache-tomcat-10.1.15.zip"

try {
    Write-Host "Downloading Apache Tomcat..."
    Invoke-WebRequest -Uri $tomcatUrl -OutFile $tomcatFile -UseBasicParsing
    Write-Host "✓ Apache Tomcat downloaded"
    
    # Upload to Azure Artifacts
    $tomcatUploaded = Upload-Package -PackageName "apache-tomcat" -Version "10.1.15" -FilePath $tomcatFile -Description "Apache Tomcat 10.1.15 for Windows x64"
    
    if ($tomcatUploaded) {
        Write-Host "✅ Apache Tomcat uploaded successfully" -ForegroundColor Green
    }
}
catch {
    Write-Host "❌ Failed to process Apache Tomcat: $_" -ForegroundColor Red
}

# Verify uploads by checking feed
Write-Host "`n=== Verifying Uploads ===" -ForegroundColor Cyan

$feedUrl = "https://pkgs.dev.azure.com/$Organization/$Project/_packaging/$FeedName/upack/index.json"
$encodedToken = [Convert]::ToBase64String([Text.Encoding]::ASCII.GetBytes(":$PersonalAccessToken"))
$headers = @{
    "Authorization" = "Basic $encodedToken"
}

try {
    Write-Host "Checking feed contents..."
    $feedResponse = Invoke-RestMethod -Uri $feedUrl -Headers $headers -Method GET
    Write-Host "✓ Feed is accessible" -ForegroundColor Green
    
    # List packages in feed (this might not work with all feed types, but worth trying)
    Write-Host "`nFeed verification complete. Check Azure DevOps web interface to see uploaded packages."
}
catch {
    Write-Host "⚠ Could not verify feed contents, but uploads may have succeeded" -ForegroundColor Yellow
    Write-Host "Check Azure DevOps web interface to verify packages"
}

# Cleanup
Write-Host "`n=== Cleanup ===" -ForegroundColor Cyan
try {
    Remove-Item -Path $tempDir -Recurse -Force
    Write-Host "✓ Cleaned up temp directory"
}
catch {
    Write-Host "⚠ Could not clean up temp directory: $tempDir"
}

Write-Host "`n=== Upload Complete ===" -ForegroundColor Green
Write-Host "Next steps:"
Write-Host "1. Go to Azure DevOps → Artifacts → $FeedName"
Write-Host "2. Verify packages are listed:"
Write-Host "   - java-jdk v17.0.0"
Write-Host "   - apache-tomcat v10.1.15"
Write-Host "3. Run your Azure Pipeline to deploy these packages"
Write-Host ""
Write-Host "If packages don't appear, check:"
Write-Host "- Personal Access Token has 'Packaging (read, write, & manage)' permissions"
Write-Host "- Feed name and organization/project are correct"
Write-Host "- Network connectivity to Azure DevOps"
