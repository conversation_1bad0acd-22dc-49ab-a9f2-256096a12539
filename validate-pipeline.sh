#!/bin/bash

# Pipeline validation script for macOS/Linux
# This script validates the Azure pipeline YAML structure and requirements

echo "=== Azure Pipeline Validation ==="

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Check if pipeline file exists
PIPELINE_FILE="azure-pipelines.yml"
if [ ! -f "$PIPELINE_FILE" ]; then
    echo -e "${RED}✗ Pipeline file not found: $PIPELINE_FILE${NC}"
    exit 1
fi

echo -e "${GREEN}✓ Pipeline file found: $PIPELINE_FILE${NC}"

# Read pipeline content
if ! CONTENT=$(cat "$PIPELINE_FILE" 2>/dev/null); then
    echo -e "${RED}✗ Failed to read pipeline file${NC}"
    exit 1
fi

echo -e "${GREEN}✓ Pipeline file readable${NC}"

# Check for required sections
echo -e "\n${CYAN}=== Checking Required Sections ===${NC}"
REQUIRED_SECTIONS=("trigger:" "variables:" "pool:" "stages:")

for section in "${REQUIRED_SECTIONS[@]}"; do
    if grep -q "$section" "$PIPELINE_FILE"; then
        echo -e "${GREEN}✓ Found: $section${NC}"
    else
        echo -e "${RED}✗ Missing: $section${NC}"
    fi
done

# Check for required variables
echo -e "\n${CYAN}=== Checking Required Variables ===${NC}"
REQUIRED_VARIABLES=("JAVA_VERSION" "JAVA_INSTALL_PATH" "JAVA_HOME_PATH" "ARTIFACTS_FEED" "ARTIFACTS_PACKAGE")

for variable in "${REQUIRED_VARIABLES[@]}"; do
    if grep -q "$variable" "$PIPELINE_FILE"; then
        echo -e "${GREEN}✓ Found variable: $variable${NC}"
    else
        echo -e "${RED}✗ Missing variable: $variable${NC}"
    fi
done

# Check for required tasks
echo -e "\n${CYAN}=== Checking Required Tasks ===${NC}"
REQUIRED_TASKS=("UniversalPackages@0" "PowerShell@2" "PublishBuildArtifacts@1")

for task in "${REQUIRED_TASKS[@]}"; do
    if grep -q "$task" "$PIPELINE_FILE"; then
        echo -e "${GREEN}✓ Found task: $task${NC}"
    else
        echo -e "${RED}✗ Missing task: $task${NC}"
    fi
done

# Check for PowerShell script blocks
echo -e "\n${CYAN}=== PowerShell Script Analysis ===${NC}"
SCRIPT_BLOCKS=$(grep -c "script: |" "$PIPELINE_FILE")
echo "Found $SCRIPT_BLOCKS PowerShell script blocks"

if [ "$SCRIPT_BLOCKS" -ge 4 ]; then
    echo -e "${GREEN}✓ Sufficient PowerShell scripts found${NC}"
else
    echo -e "${YELLOW}⚠ Expected at least 4 PowerShell script blocks${NC}"
fi

# Check for stages
echo -e "\n${CYAN}=== Stage Analysis ===${NC}"
STAGES=$(grep -c "- stage:" "$PIPELINE_FILE")
echo "Found $STAGES stages"

if [ "$STAGES" -ge 2 ]; then
    echo -e "${GREEN}✓ Multiple stages found${NC}"
else
    echo -e "${YELLOW}⚠ Expected at least 2 stages${NC}"
fi

# List the stages found
echo "Stages found:"
grep "- stage:" "$PIPELINE_FILE" | sed 's/.*stage: /  - /' | sed 's/displayName:.*//'

# Check YAML basic syntax (indentation)
echo -e "\n${CYAN}=== YAML Syntax Validation ===${NC}"
TAB_COUNT=$(grep -c $'\t' "$PIPELINE_FILE")

if [ "$TAB_COUNT" -eq 0 ]; then
    echo -e "${GREEN}✓ No tabs found (using spaces for indentation)${NC}"
else
    echo -e "${YELLOW}⚠ Found $TAB_COUNT lines with tabs (should use spaces)${NC}"
fi

# Check for common YAML issues
TRAILING_SPACES=$(grep -c ' $' "$PIPELINE_FILE")
if [ "$TRAILING_SPACES" -eq 0 ]; then
    echo -e "${GREEN}✓ No trailing spaces found${NC}"
else
    echo -e "${YELLOW}⚠ Found $TRAILING_SPACES lines with trailing spaces${NC}"
fi

# Check for Windows-specific paths
echo -e "\n${CYAN}=== Windows Path Analysis ===${NC}"
if grep -q "C:\\\\" "$PIPELINE_FILE"; then
    echo -e "${GREEN}✓ Found Windows paths (C:\\)${NC}"
else
    echo -e "${YELLOW}⚠ No Windows paths found - verify target is Windows${NC}"
fi

# Check for PowerShell-specific commands
POWERSHELL_COMMANDS=("New-Item" "Test-Path" "Get-ChildItem" "Copy-Item")
echo "PowerShell commands found:"
for cmd in "${POWERSHELL_COMMANDS[@]}"; do
    COUNT=$(grep -c "$cmd" "$PIPELINE_FILE")
    if [ "$COUNT" -gt 0 ]; then
        echo -e "  ${GREEN}✓ $cmd: $COUNT occurrences${NC}"
    fi
done

# Summary
echo -e "\n${CYAN}=== Pipeline Summary ===${NC}"
echo "File size: $(wc -c < "$PIPELINE_FILE") bytes"
echo "Line count: $(wc -l < "$PIPELINE_FILE") lines"
echo "Stages: $STAGES"
echo "PowerShell blocks: $SCRIPT_BLOCKS"

echo -e "\n${GREEN}=== Validation Complete ===${NC}"
echo "Review any warnings or errors above before running the pipeline."
echo ""
echo "Next steps for testing:"
echo "1. Set up Azure DevOps environment"
echo "2. Create Azure Artifacts feed with Java JDK"
echo "3. Configure pipeline variables"
echo "4. Test on a Windows server"
