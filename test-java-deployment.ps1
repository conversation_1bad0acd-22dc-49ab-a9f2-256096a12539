# Test script for Java deployment pipeline components
# This script simulates the pipeline steps locally for testing

param(
    [string]$JavaInstallPath = "C:\Java",
    [string]$JavaVersion = "17",
    [string]$TestJavaPackagePath = ".\test-java-package"  # Path to test Java package
)

$JavaHomePath = "$JavaInstallPath\jdk-$JavaVersion"
$BackupPath = "$JavaInstallPath\backup"

Write-Host "=== Java Deployment Test Script ===" -ForegroundColor Green
Write-Host "Java Install Path: $JavaInstallPath"
Write-Host "Java Home Path: $JavaHomePath"
Write-Host "Test Package Path: $TestJavaPackagePath"

# Function to simulate pipeline variable setting
function Set-PipelineVariable {
    param($Name, $Value)
    Write-Host "##vso[task.setvariable variable=$Name]$Value" -ForegroundColor Yellow
}

# Test 1: Check Current Java Installation
Write-Host "`n=== Test 1: Check Current Java Installation ===" -ForegroundColor Cyan
if (Test-Path $JavaHomePath) {
    Write-Host "Current Java installation found at: $JavaHomePath"
    
    try {
        $javaExe = Join-Path $JavaHomePath "bin\java.exe"
        if (Test-Path $javaExe) {
            $currentVersion = & $javaExe -version 2>&1 | Select-String "version" | Select-Object -First 1
            Write-Host "Current Java version: $currentVersion"
            
            # Create backup directory
            New-Item -ItemType Directory -Force -Path $BackupPath | Out-Null
            
            # Simulate backup creation
            $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
            $backupDir = Join-Path $BackupPath "java_backup_$timestamp"
            Write-Host "Would create backup at: $backupDir"
            
            Set-PipelineVariable "JavaBackupCreated" "true"
            Set-PipelineVariable "JavaBackupPath" $backupDir
        }
    } catch {
        Write-Warning "Could not determine current Java version: $_"
    }
} else {
    Write-Host "No existing Java installation found at: $JavaHomePath"
    Set-PipelineVariable "JavaBackupCreated" "false"
}

# Test 2: Simulate Package Download and Extraction
Write-Host "`n=== Test 2: Package Download Simulation ===" -ForegroundColor Cyan
$stagingDir = ".\java-staging-test"
New-Item -ItemType Directory -Force -Path $stagingDir | Out-Null

if (Test-Path $TestJavaPackagePath) {
    Write-Host "Test Java package found at: $TestJavaPackagePath"
    
    # Simulate package extraction
    $javaPackage = Get-ChildItem -Path $TestJavaPackagePath -Include "*.zip" -Recurse | Select-Object -First 1
    
    if ($javaPackage) {
        Write-Host "Found Java package: $($javaPackage.FullName)"
        Write-Host "Would extract to: $stagingDir"
        
        # Actually extract for testing
        try {
            Expand-Archive -Path $javaPackage.FullName -DestinationPath $stagingDir -Force
            Write-Host "Package extracted successfully"
            Set-PipelineVariable "JavaPackageReady" "true"
        } catch {
            Write-Error "Failed to extract package: $_"
            Set-PipelineVariable "JavaPackageReady" "false"
        }
    } else {
        Write-Warning "No zip file found in test package directory"
        Set-PipelineVariable "JavaPackageReady" "false"
    }
} else {
    Write-Warning "Test Java package not found at: $TestJavaPackagePath"
    Write-Host "Create a test directory with a Java JDK zip file to test extraction"
    Set-PipelineVariable "JavaPackageReady" "false"
}

# Test 3: Environment Variable Updates (Simulation)
Write-Host "`n=== Test 3: Environment Variable Updates (Simulation) ===" -ForegroundColor Cyan
Write-Host "Current JAVA_HOME: $env:JAVA_HOME"
Write-Host "Would set JAVA_HOME to: $JavaHomePath"

$javaBinPath = Join-Path $JavaHomePath "bin"
Write-Host "Would add to PATH: $javaBinPath"

# Test 4: Installation Verification
Write-Host "`n=== Test 4: Installation Verification ===" -ForegroundColor Cyan
$javaExe = "$JavaHomePath\bin\java.exe"

if (Test-Path $javaExe) {
    Write-Host "Java executable found at: $javaExe"
    try {
        Write-Host "Java version check:"
        & $javaExe -version
        Write-Host "Installation verification: PASSED" -ForegroundColor Green
    } catch {
        Write-Warning "Could not run java -version: $_"
    }
} else {
    Write-Host "Java executable not found at: $javaExe"
    Write-Host "Installation verification: FAILED" -ForegroundColor Red
}

# Test 5: Cleanup Simulation
Write-Host "`n=== Test 5: Cleanup Simulation ===" -ForegroundColor Cyan
if (Test-Path $BackupPath) {
    $backups = Get-ChildItem -Path $BackupPath -Directory | Sort-Object CreationTime -Descending
    Write-Host "Found $($backups.Count) backup directories"
    
    if ($backups.Count -gt 3) {
        $backupsToRemove = $backups | Select-Object -Skip 3
        Write-Host "Would remove $($backupsToRemove.Count) old backups:"
        foreach ($backup in $backupsToRemove) {
            Write-Host "  - $($backup.Name)"
        }
    } else {
        Write-Host "No cleanup needed (≤3 backups)"
    }
}

# Cleanup test artifacts
Write-Host "`n=== Cleaning up test artifacts ===" -ForegroundColor Cyan
if (Test-Path $stagingDir) {
    Remove-Item -Path $stagingDir -Recurse -Force
    Write-Host "Removed staging directory: $stagingDir"
}

Write-Host "`n=== Test Complete ===" -ForegroundColor Green
