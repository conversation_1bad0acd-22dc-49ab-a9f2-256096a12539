# Windows VM Server Setup for Azure Pipeline Testing

## 🖥️ **Converting Your Parallels Windows VM to a Test Server**

This guide will help you configure your Windows VM in Parallels to simulate a Windows Server environment for testing your Java + Tomcat deployment pipeline.

## 📋 **Phase 1: Basic Windows VM Configuration**

### 1. **VM Resource Allocation**
```bash
# Recommended VM settings in Parallels:
- RAM: 4GB minimum (8GB preferred)
- CPU: 2-4 cores
- Disk: 60GB minimum (for Java, Tomcat, and backups)
- Network: Shared Network (for internet access)
```

### 2. **Windows Version Check**
```powershell
# Run this in your Windows VM to check version:
Get-ComputerInfo | Select-Object WindowsProductName, WindowsVersion, TotalPhysicalMemory
```

### 3. **Enable Administrator Account**
```powershell
# Run as Administrator in Command Prompt:
net user administrator /active:yes
net user administrator YourStrongPassword123!

# Or create a new admin user:
net user pipelineadmin YourPassword123! /add
net localgroup administrators pipelineadmin /add
```

## 🔧 **Phase 2: Server-Like Configuration**

### 1. **PowerShell Configuration**
```powershell
# Run PowerShell as Administrator:

# Set execution policy
Set-ExecutionPolicy RemoteSigned -Scope LocalMachine -Force

# Enable PowerShell remoting (for Azure DevOps agent)
Enable-PSRemoting -Force
Set-Item WSMan:\localhost\Client\TrustedHosts -Value "*" -Force

# Verify PowerShell version
$PSVersionTable.PSVersion
```

### 2. **Create Directory Structure**
```powershell
# Create the directories your pipeline expects:
New-Item -ItemType Directory -Path "C:\Java" -Force
New-Item -ItemType Directory -Path "C:\Tomcat" -Force
New-Item -ItemType Directory -Path "C:\PipelineAgent" -Force
New-Item -ItemType Directory -Path "C:\Logs" -Force

# Set permissions (run as Administrator)
icacls "C:\Java" /grant "Everyone:(OI)(CI)F" /T
icacls "C:\Tomcat" /grant "Everyone:(OI)(CI)F" /T
```

### 3. **Install Required Software**
```powershell
# Install Chocolatey (package manager)
Set-ExecutionPolicy Bypass -Scope Process -Force
[System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072
iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))

# Install useful tools
choco install git -y
choco install 7zip -y
choco install notepadplusplus -y
choco install curl -y
```

## 🌐 **Phase 3: Network Configuration**

### 1. **Configure Windows Firewall**
```powershell
# Allow common ports for testing
New-NetFirewallRule -DisplayName "Allow Port 8080" -Direction Inbound -Protocol TCP -LocalPort 8080 -Action Allow
New-NetFirewallRule -DisplayName "Allow Port 22" -Direction Inbound -Protocol TCP -LocalPort 22 -Action Allow
New-NetFirewallRule -DisplayName "Allow Port 5985" -Direction Inbound -Protocol TCP -LocalPort 5985 -Action Allow

# Or disable firewall for testing (less secure)
# Set-NetFirewallProfile -Profile Domain,Public,Private -Enabled False
```

### 2. **Get VM Network Information**
```powershell
# Find your VM's IP address
Get-NetIPAddress -AddressFamily IPv4 | Where-Object {$_.IPAddress -notlike "127.*"} | Select-Object IPAddress, InterfaceAlias

# Test network connectivity
Test-NetConnection -ComputerName google.com -Port 80
```

## 🤖 **Phase 4: Azure DevOps Agent Setup**

### 1. **Download and Install Azure DevOps Agent**
```powershell
# Create agent directory
cd C:\PipelineAgent

# Download agent (replace with latest version URL from Azure DevOps)
Invoke-WebRequest -Uri "https://vstsagentpackage.azureedge.net/agent/3.232.0/vsts-agent-win-x64-3.232.0.zip" -OutFile "agent.zip"

# Extract agent
Expand-Archive -Path "agent.zip" -DestinationPath "." -Force
Remove-Item "agent.zip"
```

### 2. **Configure Agent**
```powershell
# Run the configuration script
.\config.cmd

# You'll be prompted for:
# - Server URL: https://dev.azure.com/YourOrganization
# - Authentication type: PAT (Personal Access Token)
# - Personal Access Token: (create one in Azure DevOps)
# - Agent pool: Default
# - Agent name: WindowsVM-Agent
# - Work folder: _work
# - Run as service: Y
# - User account: Use the admin account you created
```

### 3. **Start Agent as Service**
```powershell
# Install and start the agent service
.\svc.cmd install
.\svc.cmd start

# Verify service is running
Get-Service -Name "vstsagent*"
```

## 🧪 **Phase 5: Testing Environment Setup**

### 1. **Create Test Scripts**
```powershell
# Create a test script to verify your setup
$testScript = @'
Write-Host "=== Windows VM Server Test ==="
Write-Host "PowerShell Version: $($PSVersionTable.PSVersion)"
Write-Host "Current User: $(whoami)"
Write-Host "Admin Rights: $((New-Object Security.Principal.WindowsPrincipal([Security.Principal.WindowsIdentity]::GetCurrent())).IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator))"
Write-Host "Java Directory: $(Test-Path 'C:\Java')"
Write-Host "Tomcat Directory: $(Test-Path 'C:\Tomcat')"
Write-Host "Network IP: $((Get-NetIPAddress -AddressFamily IPv4 | Where-Object {$_.IPAddress -notlike '127.*'}).IPAddress)"
Write-Host "Disk Space: $([math]::Round((Get-WmiObject -Class Win32_LogicalDisk -Filter "DeviceID='C:'").FreeSpace / 1GB, 2)) GB free"
'@

$testScript | Out-File -FilePath "C:\test-server-setup.ps1" -Encoding UTF8
```

### 2. **Run Test Script**
```powershell
# Run the test script
PowerShell -ExecutionPolicy Bypass -File "C:\test-server-setup.ps1"
```

## 🔐 **Phase 6: Security Configuration**

### 1. **User Account Control (UAC)**
```powershell
# Disable UAC for testing (reboot required)
Set-ItemProperty -Path "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\System" -Name "EnableLUA" -Value 0

# Or configure UAC to not prompt for admin operations
Set-ItemProperty -Path "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\System" -Name "ConsentPromptBehaviorAdmin" -Value 0
```

### 2. **Windows Defender (Optional)**
```powershell
# Exclude pipeline directories from Windows Defender scanning
Add-MpPreference -ExclusionPath "C:\Java"
Add-MpPreference -ExclusionPath "C:\Tomcat"
Add-MpPreference -ExclusionPath "C:\PipelineAgent"
```

## 📊 **Phase 7: Environment Validation**

### 1. **Create Validation Script**
```powershell
# Save this as C:\validate-pipeline-environment.ps1
$validationScript = @'
Write-Host "=== Pipeline Environment Validation ==="

# Check directories
$dirs = @("C:\Java", "C:\Tomcat", "C:\PipelineAgent")
foreach ($dir in $dirs) {
    if (Test-Path $dir) {
        Write-Host "✓ Directory exists: $dir"
        try {
            $testFile = Join-Path $dir "test.txt"
            "test" | Out-File -FilePath $testFile
            Remove-Item $testFile
            Write-Host "  ✓ Write permissions OK"
        } catch {
            Write-Host "  ✗ Write permissions FAILED: $_"
        }
    } else {
        Write-Host "✗ Directory missing: $dir"
    }
}

# Check PowerShell
Write-Host "✓ PowerShell Version: $($PSVersionTable.PSVersion)"
Write-Host "✓ Execution Policy: $(Get-ExecutionPolicy)"

# Check admin rights
$isAdmin = ([Security.Principal.WindowsPrincipal][Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
if ($isAdmin) {
    Write-Host "✓ Running as Administrator"
} else {
    Write-Host "✗ NOT running as Administrator"
}

# Check network
try {
    $response = Invoke-WebRequest -Uri "https://dev.azure.com" -UseBasicParsing -TimeoutSec 10
    Write-Host "✓ Internet connectivity OK"
} catch {
    Write-Host "✗ Internet connectivity FAILED: $_"
}

# Check agent service
$agentService = Get-Service -Name "vstsagent*" -ErrorAction SilentlyContinue
if ($agentService) {
    Write-Host "✓ Azure DevOps Agent Service: $($agentService.Status)"
} else {
    Write-Host "✗ Azure DevOps Agent Service not found"
}

Write-Host "=== Validation Complete ==="
'@

$validationScript | Out-File -FilePath "C:\validate-pipeline-environment.ps1" -Encoding UTF8
```

### 2. **Run Validation**
```powershell
PowerShell -ExecutionPolicy Bypass -File "C:\validate-pipeline-environment.ps1"
```

## 🚀 **Phase 8: Azure DevOps Integration**

### 1. **Create Environment in Azure DevOps**
```yaml
# In Azure DevOps:
1. Go to Pipelines > Environments
2. Create new environment: "windows-servers"
3. Add resource > Virtual Machine
4. Use your VM's IP address or computer name
5. Configure authentication (username/password or certificate)
```

### 2. **Test Pipeline Connection**
```powershell
# Create a simple test pipeline to verify connection
# This should be done in Azure DevOps, but you can prepare by ensuring
# your VM can be reached from Azure DevOps
```

## 📝 **Quick Setup Checklist**

```bash
□ VM has 4GB+ RAM and 60GB+ disk
□ Windows admin account created
□ PowerShell execution policy set to RemoteSigned
□ Directories created: C:\Java, C:\Tomcat, C:\PipelineAgent
□ Directory permissions set correctly
□ Windows Firewall configured (ports 8080, 5985 open)
□ Azure DevOps agent downloaded and configured
□ Agent service running
□ Validation script passes all checks
□ Environment created in Azure DevOps
□ VM registered as environment resource
```

## 🎯 **Next Steps**

1. **Run the validation script** to ensure everything is set up correctly
2. **Test a simple pipeline** first (just echo commands)
3. **Gradually add complexity** (file operations, then Java/Tomcat deployment)
4. **Monitor Azure DevOps logs** for any issues

Your Windows VM should now be ready to act as a test server for your Azure pipeline!
