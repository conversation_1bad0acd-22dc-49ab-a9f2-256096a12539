# Simple Azure Pipeline - No Azure Artifacts Required
# Downloads Java and Tomcat from public sources

trigger: none

parameters:
  - name: deployJava
    displayName: 'Deploy Java'
    type: boolean
    default: true
  - name: deployTomcat
    displayName: 'Deploy Tomcat'
    type: boolean
    default: false

variables:
  JAVA_INSTALL_PATH: 'C:\Java'
  TOMCAT_INSTALL_PATH: 'C:\Tomcat'

pool:
  vmImage: 'windows-latest'

stages:
- stage: Deploy
  jobs:
  - deployment: DeployToServer
    environment: 'windows-servers'
    strategy:
      runOnce:
        deploy:
          steps:
          - task: PowerShell@2
            displayName: 'Deploy Java'
            condition: eq('${{ parameters.deployJava }}', true)
            inputs:
              targetType: 'inline'
              script: |
                Write-Host "=== Installing Java ==="
                
                # Create directory
                New-Item -ItemType Directory -Path "$(JAVA_INSTALL_PATH)" -Force
                
                # Download Java
                $javaUrl = "https://github.com/adoptium/temurin17-binaries/releases/download/jdk-17.0.9%2B9/OpenJDK17U-jdk_x64_windows_hotspot_17.0.9_9.zip"
                $javaZip = "C:\temp\java.zip"
                
                Write-Host "Downloading Java..."
                Invoke-WebRequest -Uri $javaUrl -OutFile $javaZip -UseBasicParsing
                
                # Extract Java
                Write-Host "Extracting Java..."
                Expand-Archive -Path $javaZip -DestinationPath "C:\temp\java" -Force
                
                # Find JDK directory and copy to final location
                $jdkDir = Get-ChildItem -Path "C:\temp\java" -Directory | Select-Object -First 1
                $targetDir = "$(JAVA_INSTALL_PATH)\jdk-17"
                
                if (Test-Path $targetDir) {
                    Remove-Item -Path $targetDir -Recurse -Force
                }
                
                Copy-Item -Path $jdkDir.FullName -Destination $targetDir -Recurse -Force
                
                # Set environment variable
                [Environment]::SetEnvironmentVariable("JAVA_HOME", $targetDir, "Machine")
                
                # Verify
                $javaExe = "$targetDir\bin\java.exe"
                if (Test-Path $javaExe) {
                    Write-Host "✓ Java installed successfully!" -ForegroundColor Green
                    & $javaExe -version
                } else {
                    Write-Error "Java installation failed"
                }
                
                # Cleanup
                Remove-Item -Path "C:\temp\java*" -Recurse -Force -ErrorAction SilentlyContinue

          - task: PowerShell@2
            displayName: 'Deploy Tomcat'
            condition: eq('${{ parameters.deployTomcat }}', true)
            inputs:
              targetType: 'inline'
              script: |
                Write-Host "=== Installing Tomcat ==="
                
                # Create directory
                New-Item -ItemType Directory -Path "$(TOMCAT_INSTALL_PATH)" -Force
                
                # Download Tomcat
                $tomcatUrl = "https://archive.apache.org/dist/tomcat/tomcat-10/v10.1.15/bin/apache-tomcat-10.1.15-windows-x64.zip"
                $tomcatZip = "C:\temp\tomcat.zip"
                
                Write-Host "Downloading Tomcat..."
                Invoke-WebRequest -Uri $tomcatUrl -OutFile $tomcatZip -UseBasicParsing
                
                # Extract Tomcat
                Write-Host "Extracting Tomcat..."
                Expand-Archive -Path $tomcatZip -DestinationPath "C:\temp\tomcat" -Force
                
                # Find Tomcat directory and copy to final location
                $tomcatDir = Get-ChildItem -Path "C:\temp\tomcat" -Directory | Select-Object -First 1
                $targetDir = "$(TOMCAT_INSTALL_PATH)\apache-tomcat-10.1.15"
                
                if (Test-Path $targetDir) {
                    Remove-Item -Path $targetDir -Recurse -Force
                }
                
                Copy-Item -Path $tomcatDir.FullName -Destination $targetDir -Recurse -Force
                
                # Set environment variable
                [Environment]::SetEnvironmentVariable("CATALINA_HOME", $targetDir, "Machine")
                
                # Create setenv.bat
                $javaHome = [Environment]::GetEnvironmentVariable("JAVA_HOME", "Machine")
                if ($javaHome) {
                    $setenvFile = "$targetDir\bin\setenv.bat"
                    @"
@echo off
set JAVA_HOME=$javaHome
set CATALINA_HOME=$targetDir
echo Tomcat configured with JAVA_HOME: %JAVA_HOME%
"@ | Out-File -FilePath $setenvFile -Encoding ASCII
                    Write-Host "✓ Created setenv.bat"
                }
                
                # Verify
                $catalinaScript = "$targetDir\bin\catalina.bat"
                if (Test-Path $catalinaScript) {
                    Write-Host "✓ Tomcat installed successfully!" -ForegroundColor Green
                } else {
                    Write-Error "Tomcat installation failed"
                }
                
                # Cleanup
                Remove-Item -Path "C:\temp\tomcat*" -Recurse -Force -ErrorAction SilentlyContinue

          - task: PowerShell@2
            displayName: 'Verification'
            inputs:
              targetType: 'inline'
              script: |
                Write-Host "=== Installation Summary ===" -ForegroundColor Green
                
                if ('${{ parameters.deployJava }}' -eq 'true') {
                    $javaHome = [Environment]::GetEnvironmentVariable("JAVA_HOME", "Machine")
                    Write-Host "Java Home: $javaHome"
                    if (Test-Path "$javaHome\bin\java.exe") {
                        Write-Host "✓ Java is ready" -ForegroundColor Green
                    }
                }
                
                if ('${{ parameters.deployTomcat }}' -eq 'true') {
                    $catalinaHome = [Environment]::GetEnvironmentVariable("CATALINA_HOME", "Machine")
                    Write-Host "Catalina Home: $catalinaHome"
                    if (Test-Path "$catalinaHome\bin\catalina.bat") {
                        Write-Host "✓ Tomcat is ready" -ForegroundColor Green
                    }
                }
                
                Write-Host "`nNext steps:"
                Write-Host "1. Test Java: java -version"
                Write-Host "2. Start Tomcat: %CATALINA_HOME%\bin\startup.bat"
                Write-Host "3. Check Tomcat: http://localhost:8080"
