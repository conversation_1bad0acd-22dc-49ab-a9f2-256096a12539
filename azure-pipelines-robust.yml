# Azure DevOps Pipeline for Java + Tomcat Deployment (Production-Ready Version)
# This is a more robust version with better error handling and validation

trigger: none  # Manual trigger only for safety

pr: none

parameters:
  - name: deployJava
    displayName: 'Deploy Java'
    type: boolean
    default: true
  - name: deployTomcat
    displayName: 'Deploy Tomcat'
    type: boolean
    default: true
  - name: skipBackup
    displayName: 'Skip Backup (for testing)'
    type: boolean
    default: false

variables:
  # Java configuration
  JAVA_VERSION: '17'
  JAVA_INSTALL_PATH: 'C:\Java'
  JAVA_HOME_PATH: 'C:\Java\jdk-$(JAVA_VERSION)'
  
  # Tomcat configuration
  TOMCAT_VERSION: '10.1'
  TOMCAT_INSTALL_PATH: 'C:\Tomcat'
  TOMCAT_HOME_PATH: 'C:\Tomcat\apache-tomcat-$(TOMCAT_VERSION)'
  
  # Azure Artifacts configuration - MUST BE SET IN PIPELINE VARIABLES
  ARTIFACTS_FEED: '$(ArtifactsFeedName)'
  JAVA_ARTIFACTS_PACKAGE: 'java-jdk'
  TOMCAT_ARTIFACTS_PACKAGE: 'apache-tomcat'

pool:
  vmImage: 'windows-latest'

stages:
  - stage: ValidateAndDownload
    displayName: 'Validate Prerequisites and Download Packages'
    jobs:
      - job: ValidatePrerequisites
        displayName: 'Validate Prerequisites'
        steps:
          - task: PowerShell@2
            displayName: 'Validate Pipeline Configuration'
            inputs:
              targetType: 'inline'
              script: |
                Write-Host "=== Validating Pipeline Configuration ==="
                
                # Check required variables
                $requiredVars = @('ARTIFACTS_FEED', 'JAVA_VERSION', 'TOMCAT_VERSION')
                $missingVars = @()
                
                foreach ($var in $requiredVars) {
                    $value = Get-Variable -Name $var -ValueOnly -ErrorAction SilentlyContinue
                    if ([string]::IsNullOrEmpty($value) -or $value.StartsWith('$(')) {
                        $missingVars += $var
                        Write-Warning "Variable $var is not set or not resolved: $value"
                    } else {
                        Write-Host "✓ $var = $value"
                    }
                }
                
                if ($missingVars.Count -gt 0) {
                    Write-Error "Missing required variables: $($missingVars -join ', ')"
                    Write-Host "Please set these variables in your Azure DevOps pipeline"
                    exit 1
                }
                
                # Check permissions
                Write-Host "`n=== Checking Permissions ==="
                try {
                    $testPath = "C:\temp-pipeline-test"
                    New-Item -ItemType Directory -Path $testPath -Force | Out-Null
                    Remove-Item -Path $testPath -Force
                    Write-Host "✓ File system permissions OK"
                } catch {
                    Write-Warning "Limited file system permissions: $_"
                }
                
                Write-Host "`n=== Validation Complete ==="
              pwsh: true

      - job: DownloadJava
        displayName: 'Download Java Package'
        condition: eq('${{ parameters.deployJava }}', true)
        dependsOn: ValidatePrerequisites
        steps:
          - task: UniversalPackages@0
            displayName: 'Download Java from Azure Artifacts'
            inputs:
              command: 'download'
              downloadDirectory: '$(Pipeline.Workspace)/java-download'
              feedsToUse: 'internal'
              vstsFeed: '$(ARTIFACTS_FEED)'
              vstsFeedPackage: '$(JAVA_ARTIFACTS_PACKAGE)'
              vstsPackageVersion: '*'
              verbosity: 'Information'
            continueOnError: true

          - task: PowerShell@2
            displayName: 'Validate and Prepare Java Package'
            inputs:
              targetType: 'inline'
              script: |
                $downloadDir = "$(Pipeline.Workspace)/java-download"
                $stagingDir = "$(Pipeline.Workspace)/java-staging"
                
                Write-Host "Looking for Java package in: $downloadDir"
                
                if (-not (Test-Path $downloadDir)) {
                    Write-Error "Download directory not found. Check Azure Artifacts configuration."
                    exit 1
                }
                
                # Find any archive files
                $packages = Get-ChildItem -Path $downloadDir -Recurse -Include "*.zip", "*.tar.gz", "*.7z"
                
                if ($packages.Count -eq 0) {
                    Write-Error "No Java package found. Expected zip, tar.gz, or 7z file."
                    Get-ChildItem -Path $downloadDir -Recurse | ForEach-Object { Write-Host "Found: $($_.FullName)" }
                    exit 1
                }
                
                $javaPackage = $packages[0]
                Write-Host "Found Java package: $($javaPackage.FullName)"
                
                # Create staging directory
                New-Item -ItemType Directory -Force -Path $stagingDir | Out-Null
                
                # Extract package
                try {
                    if ($javaPackage.Extension -eq ".zip") {
                        Expand-Archive -Path $javaPackage.FullName -DestinationPath $stagingDir -Force
                        Write-Host "✓ Java package extracted successfully"
                    } else {
                        Write-Warning "Non-zip package found. Manual extraction may be needed."
                        # Copy the package to staging for manual handling
                        Copy-Item -Path $javaPackage.FullName -Destination $stagingDir
                    }
                } catch {
                    Write-Error "Failed to extract Java package: $_"
                    exit 1
                }
              pwsh: true

          - task: PublishBuildArtifacts@1
            displayName: 'Publish Java Package'
            inputs:
              pathToPublish: '$(Pipeline.Workspace)/java-staging'
              artifactName: 'java-package'
              publishLocation: 'Container'

      - job: DownloadTomcat
        displayName: 'Download Tomcat Package'
        condition: eq('${{ parameters.deployTomcat }}', true)
        dependsOn: ValidatePrerequisites
        steps:
          - task: UniversalPackages@0
            displayName: 'Download Tomcat from Azure Artifacts'
            inputs:
              command: 'download'
              downloadDirectory: '$(Pipeline.Workspace)/tomcat-download'
              feedsToUse: 'internal'
              vstsFeed: '$(ARTIFACTS_FEED)'
              vstsFeedPackage: '$(TOMCAT_ARTIFACTS_PACKAGE)'
              vstsPackageVersion: '*'
              verbosity: 'Information'
            continueOnError: true

          - task: PowerShell@2
            displayName: 'Validate and Prepare Tomcat Package'
            inputs:
              targetType: 'inline'
              script: |
                $downloadDir = "$(Pipeline.Workspace)/tomcat-download"
                $stagingDir = "$(Pipeline.Workspace)/tomcat-staging"
                
                Write-Host "Looking for Tomcat package in: $downloadDir"
                
                if (-not (Test-Path $downloadDir)) {
                    Write-Error "Download directory not found. Check Azure Artifacts configuration."
                    exit 1
                }
                
                # Find any archive files
                $packages = Get-ChildItem -Path $downloadDir -Recurse -Include "*.zip", "*.tar.gz", "*.7z"
                
                if ($packages.Count -eq 0) {
                    Write-Error "No Tomcat package found. Expected zip, tar.gz, or 7z file."
                    Get-ChildItem -Path $downloadDir -Recurse | ForEach-Object { Write-Host "Found: $($_.FullName)" }
                    exit 1
                }
                
                $tomcatPackage = $packages[0]
                Write-Host "Found Tomcat package: $($tomcatPackage.FullName)"
                
                # Create staging directory
                New-Item -ItemType Directory -Force -Path $stagingDir | Out-Null
                
                # Extract package
                try {
                    if ($tomcatPackage.Extension -eq ".zip") {
                        Expand-Archive -Path $tomcatPackage.FullName -DestinationPath $stagingDir -Force
                        Write-Host "✓ Tomcat package extracted successfully"
                    } else {
                        Write-Warning "Non-zip package found. Manual extraction may be needed."
                        # Copy the package to staging for manual handling
                        Copy-Item -Path $tomcatPackage.FullName -Destination $stagingDir
                    }
                } catch {
                    Write-Error "Failed to extract Tomcat package: $_"
                    exit 1
                }
              pwsh: true

          - task: PublishBuildArtifacts@1
            displayName: 'Publish Tomcat Package'
            inputs:
              pathToPublish: '$(Pipeline.Workspace)/tomcat-staging'
              artifactName: 'tomcat-package'
              publishLocation: 'Container'

  - stage: DeployApplications
    displayName: 'Deploy Java and Tomcat'
    dependsOn: ValidateAndDownload
    condition: succeeded()
    jobs:
      - deployment: DeployToServer
        displayName: 'Deploy to Target Server'
        environment: 'windows-servers'  # This environment must exist in Azure DevOps
        strategy:
          runOnce:
            deploy:
              steps:
                - download: current
                  artifact: 'java-package'
                  displayName: 'Download Java Package'
                  condition: eq('${{ parameters.deployJava }}', true)

                - download: current
                  artifact: 'tomcat-package'
                  displayName: 'Download Tomcat Package'
                  condition: eq('${{ parameters.deployTomcat }}', true)

                - task: PowerShell@2
                  displayName: 'Pre-Deployment Validation'
                  inputs:
                    targetType: 'inline'
                    script: |
                      Write-Host "=== Pre-Deployment Validation ==="

                      # Check if running with sufficient privileges
                      $currentPrincipal = New-Object Security.Principal.WindowsPrincipal([Security.Principal.WindowsIdentity]::GetCurrent())
                      $isAdmin = $currentPrincipal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)

                      if (-not $isAdmin) {
                          Write-Warning "Not running as administrator. Some operations may fail."
                          Write-Host "Consider configuring the deployment agent to run as administrator."
                      } else {
                          Write-Host "✓ Running with administrator privileges"
                      }

                      # Check available disk space
                      $drive = Get-WmiObject -Class Win32_LogicalDisk -Filter "DeviceID='C:'"
                      $freeSpaceGB = [math]::Round($drive.FreeSpace / 1GB, 2)
                      Write-Host "✓ Available disk space: $freeSpaceGB GB"

                      if ($freeSpaceGB -lt 2) {
                          Write-Warning "Low disk space. Consider cleaning up before deployment."
                      }

                      # Check if directories can be created
                      $testDirs = @("$(JAVA_INSTALL_PATH)", "$(TOMCAT_INSTALL_PATH)")
                      foreach ($dir in $testDirs) {
                          try {
                              if (-not (Test-Path $dir)) {
                                  New-Item -ItemType Directory -Path $dir -Force | Out-Null
                                  Write-Host "✓ Created directory: $dir"
                              } else {
                                  Write-Host "✓ Directory exists: $dir"
                              }
                          } catch {
                              Write-Error "Cannot create directory $dir : $_"
                              exit 1
                          }
                      }
                    pwsh: true

                - task: PowerShell@2
                  displayName: 'Stop Tomcat Services'
                  condition: eq('${{ parameters.deployTomcat }}', true)
                  inputs:
                    targetType: 'inline'
                    script: |
                      Write-Host "=== Stopping Tomcat Services ==="

                      # Stop Tomcat services
                      $tomcatServices = Get-Service -Name "*tomcat*" -ErrorAction SilentlyContinue

                      if ($tomcatServices) {
                          foreach ($service in $tomcatServices) {
                              if ($service.Status -eq "Running") {
                                  Write-Host "Stopping service: $($service.Name)"
                                  try {
                                      Stop-Service -Name $service.Name -Force -TimeoutSec 30
                                      Write-Host "✓ Service stopped: $($service.Name)"
                                  } catch {
                                      Write-Warning "Failed to stop service $($service.Name): $_"
                                  }
                              }
                          }
                      } else {
                          Write-Host "No Tomcat services found"
                      }

                      # Stop Tomcat processes
                      $tomcatProcesses = Get-Process -Name "*java*" -ErrorAction SilentlyContinue |
                                        Where-Object { $_.ProcessName -like "*tomcat*" -or $_.MainWindowTitle -like "*tomcat*" }

                      if ($tomcatProcesses) {
                          Write-Host "Stopping Tomcat processes..."
                          $tomcatProcesses | Stop-Process -Force
                          Start-Sleep -Seconds 5
                          Write-Host "✓ Tomcat processes stopped"
                      }
                    pwsh: true
                    continueOnError: true

                - task: PowerShell@2
                  displayName: 'Deploy Java'
                  condition: eq('${{ parameters.deployJava }}', true)
                  inputs:
                    targetType: 'inline'
                    script: |
                      Write-Host "=== Deploying Java ==="

                      $javaHomePath = "$(JAVA_HOME_PATH)"
                      $backupCreated = $false

                      try {
                          # Backup existing installation
                          if ((Test-Path $javaHomePath) -and (-not '${{ parameters.skipBackup }}')) {
                              $backupDir = "$(JAVA_INSTALL_PATH)\backup\java_backup_$(Get-Date -Format 'yyyyMMdd_HHmmss')"
                              Write-Host "Creating backup: $backupDir"
                              New-Item -ItemType Directory -Path (Split-Path $backupDir) -Force | Out-Null
                              Copy-Item -Path $javaHomePath -Destination $backupDir -Recurse -Force
                              $backupCreated = $true
                              Write-Host "✓ Backup created successfully"
                          }

                          # Find Java installation files
                          $javaPackageDir = "$(Pipeline.Workspace)\java-package"
                          if (-not (Test-Path $javaPackageDir)) {
                              Write-Error "Java package directory not found: $javaPackageDir"
                              exit 1
                          }

                          # Find the JDK directory (usually the extracted folder)
                          $jdkDirs = Get-ChildItem -Path $javaPackageDir -Directory | Where-Object { $_.Name -like "*jdk*" -or $_.Name -like "*java*" }

                          if ($jdkDirs.Count -eq 0) {
                              Write-Warning "No JDK directory found. Using first directory."
                              $jdkDirs = Get-ChildItem -Path $javaPackageDir -Directory | Select-Object -First 1
                          }

                          if ($jdkDirs.Count -eq 0) {
                              Write-Error "No directories found in Java package"
                              exit 1
                          }

                          $sourceJdk = $jdkDirs[0]
                          Write-Host "Installing Java from: $($sourceJdk.FullName)"

                          # Remove existing installation
                          if (Test-Path $javaHomePath) {
                              Write-Host "Removing existing Java installation..."
                              Remove-Item -Path $javaHomePath -Recurse -Force
                          }

                          # Install new Java
                          Copy-Item -Path $sourceJdk.FullName -Destination $javaHomePath -Recurse -Force

                          # Verify installation
                          $javaExe = Join-Path $javaHomePath "bin\java.exe"
                          if (Test-Path $javaExe) {
                              Write-Host "✓ Java installed successfully"

                              # Try to get version
                              try {
                                  $version = & $javaExe -version 2>&1 | Select-Object -First 1
                                  Write-Host "Java version: $version"
                              } catch {
                                  Write-Warning "Could not get Java version: $_"
                              }

                              # Set environment variables (best effort)
                              try {
                                  [Environment]::SetEnvironmentVariable("JAVA_HOME", $javaHomePath, "Machine")
                                  Write-Host "✓ JAVA_HOME set to: $javaHomePath"
                              } catch {
                                  Write-Warning "Could not set JAVA_HOME environment variable: $_"
                                  Write-Host "You may need to set this manually or run with administrator privileges"
                              }

                          } else {
                              throw "Java installation failed - java.exe not found"
                          }

                      } catch {
                          Write-Error "Java deployment failed: $_"

                          # Attempt rollback if backup was created
                          if ($backupCreated -and (Test-Path $backupDir)) {
                              Write-Host "Attempting rollback..."
                              try {
                                  if (Test-Path $javaHomePath) {
                                      Remove-Item -Path $javaHomePath -Recurse -Force
                                  }
                                  Copy-Item -Path $backupDir -Destination $javaHomePath -Recurse -Force
                                  Write-Host "✓ Rollback completed"
                              } catch {
                                  Write-Error "Rollback failed: $_"
                              }
                          }
                          exit 1
                      }
                    pwsh: true

                - task: PowerShell@2
                  displayName: 'Deploy Tomcat'
                  condition: eq('${{ parameters.deployTomcat }}', true)
                  inputs:
                    targetType: 'inline'
                    script: |
                      Write-Host "=== Deploying Tomcat ==="

                      $tomcatHomePath = "$(TOMCAT_HOME_PATH)"
                      $backupCreated = $false

                      try {
                          # Backup existing installation
                          if ((Test-Path $tomcatHomePath) -and (-not '${{ parameters.skipBackup }}')) {
                              $backupDir = "$(TOMCAT_INSTALL_PATH)\backup\tomcat_backup_$(Get-Date -Format 'yyyyMMdd_HHmmss')"
                              Write-Host "Creating backup: $backupDir"
                              New-Item -ItemType Directory -Path (Split-Path $backupDir) -Force | Out-Null
                              Copy-Item -Path $tomcatHomePath -Destination $backupDir -Recurse -Force
                              $backupCreated = $true
                              Write-Host "✓ Backup created successfully"
                          }

                          # Find Tomcat installation files
                          $tomcatPackageDir = "$(Pipeline.Workspace)\tomcat-package"
                          if (-not (Test-Path $tomcatPackageDir)) {
                              Write-Error "Tomcat package directory not found: $tomcatPackageDir"
                              exit 1
                          }

                          # Find the Tomcat directory
                          $tomcatDirs = Get-ChildItem -Path $tomcatPackageDir -Directory | Where-Object { $_.Name -like "*tomcat*" }

                          if ($tomcatDirs.Count -eq 0) {
                              Write-Warning "No Tomcat directory found. Using first directory."
                              $tomcatDirs = Get-ChildItem -Path $tomcatPackageDir -Directory | Select-Object -First 1
                          }

                          if ($tomcatDirs.Count -eq 0) {
                              Write-Error "No directories found in Tomcat package"
                              exit 1
                          }

                          $sourceTomcat = $tomcatDirs[0]
                          Write-Host "Installing Tomcat from: $($sourceTomcat.FullName)"

                          # Remove existing installation
                          if (Test-Path $tomcatHomePath) {
                              Write-Host "Removing existing Tomcat installation..."
                              Remove-Item -Path $tomcatHomePath -Recurse -Force
                          }

                          # Install new Tomcat
                          Copy-Item -Path $sourceTomcat.FullName -Destination $tomcatHomePath -Recurse -Force

                          # Verify installation
                          $catalinaScript = Join-Path $tomcatHomePath "bin\catalina.bat"
                          if (Test-Path $catalinaScript) {
                              Write-Host "✓ Tomcat installed successfully"

                              # Set environment variables (best effort)
                              try {
                                  [Environment]::SetEnvironmentVariable("CATALINA_HOME", $tomcatHomePath, "Machine")
                                  Write-Host "✓ CATALINA_HOME set to: $tomcatHomePath"
                              } catch {
                                  Write-Warning "Could not set CATALINA_HOME environment variable: $_"
                              }

                              # Create basic setenv.bat if Java is available
                              $javaHome = [Environment]::GetEnvironmentVariable("JAVA_HOME", "Machine")
                              if ($javaHome -and (Test-Path $javaHome)) {
                                  $setenvFile = Join-Path $tomcatHomePath "bin\setenv.bat"
                                  $setenvContent = @(
                                      "@echo off",
                                      "set JAVA_HOME=$javaHome",
                                      "set CATALINA_HOME=$tomcatHomePath",
                                      "echo Tomcat configured with JAVA_HOME: %JAVA_HOME%"
                                  )
                                  $setenvContent | Out-File -FilePath $setenvFile -Encoding ASCII
                                  Write-Host "✓ Created setenv.bat configuration"
                              }

                          } else {
                              throw "Tomcat installation failed - catalina.bat not found"
                          }

                      } catch {
                          Write-Error "Tomcat deployment failed: $_"

                          # Attempt rollback if backup was created
                          if ($backupCreated -and (Test-Path $backupDir)) {
                              Write-Host "Attempting rollback..."
                              try {
                                  if (Test-Path $tomcatHomePath) {
                                      Remove-Item -Path $tomcatHomePath -Recurse -Force
                                  }
                                  Copy-Item -Path $backupDir -Destination $tomcatHomePath -Recurse -Force
                                  Write-Host "✓ Rollback completed"
                              } catch {
                                  Write-Error "Rollback failed: $_"
                              }
                          }
                          exit 1
                      }
                    pwsh: true

                - task: PowerShell@2
                  displayName: 'Final Verification and Cleanup'
                  inputs:
                    targetType: 'inline'
                    script: |
                      Write-Host "=== Final Verification ==="

                      # Verify Java installation
                      if ('${{ parameters.deployJava }}' -eq 'true') {
                          $javaExe = "$(JAVA_HOME_PATH)\bin\java.exe"
                          if (Test-Path $javaExe) {
                              Write-Host "✓ Java installation verified"
                              try {
                                  & $javaExe -version
                              } catch {
                                  Write-Warning "Java version check failed: $_"
                              }
                          } else {
                              Write-Error "Java installation verification failed"
                          }
                      }

                      # Verify Tomcat installation
                      if ('${{ parameters.deployTomcat }}' -eq 'true') {
                          $catalinaScript = "$(TOMCAT_HOME_PATH)\bin\catalina.bat"
                          if (Test-Path $catalinaScript) {
                              Write-Host "✓ Tomcat installation verified"

                              # Optional: Try to start Tomcat (commented out for safety)
                              # Write-Host "Starting Tomcat..."
                              # Start-Process -FilePath $catalinaScript -ArgumentList "start" -WorkingDirectory (Split-Path $catalinaScript)

                          } else {
                              Write-Error "Tomcat installation verification failed"
                          }
                      }

                      # Cleanup old backups (keep last 3)
                      $backupDirs = @("$(JAVA_INSTALL_PATH)\backup", "$(TOMCAT_INSTALL_PATH)\backup")
                      foreach ($backupDir in $backupDirs) {
                          if (Test-Path $backupDir) {
                              $backups = Get-ChildItem -Path $backupDir -Directory | Sort-Object CreationTime -Descending
                              if ($backups.Count -gt 3) {
                                  $toRemove = $backups | Select-Object -Skip 3
                                  foreach ($backup in $toRemove) {
                                      Write-Host "Removing old backup: $($backup.Name)"
                                      Remove-Item -Path $backup.FullName -Recurse -Force
                                  }
                              }
                          }
                      }

                      Write-Host "=== Deployment Complete ==="
                      Write-Host "Java Home: $(JAVA_HOME_PATH)"
                      Write-Host "Tomcat Home: $(TOMCAT_HOME_PATH)"
                      Write-Host ""
                      Write-Host "Next steps:"
                      Write-Host "1. Verify environment variables are set correctly"
                      Write-Host "2. Test Java: java -version"
                      Write-Host "3. Start Tomcat manually if needed"
                      Write-Host "4. Check Tomcat logs for any issues"
                    pwsh: true
                    continueOnError: true
