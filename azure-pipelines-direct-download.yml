# Azure Pipeline - Direct Download Version (No Azure Artifacts Needed)
# Downloads Java and Tomcat directly from official sources

trigger: none

parameters:
  - name: deployJava
    displayName: 'Deploy Java'
    type: boolean
    default: true
  - name: deployTomcat
    displayName: 'Deploy Tomcat'
    type: boolean
    default: true
  - name: skipBackup
    displayName: 'Skip Backup (for testing)'
    type: boolean
    default: false

variables:
  # Java configuration
  JAVA_VERSION: '17'
  JAVA_INSTALL_PATH: 'C:\Java'
  JAVA_HOME_PATH: 'C:\Java\jdk-$(JAVA_VERSION)'
  
  # Tomcat configuration
  TOMCAT_VERSION: '10.1.15'
  TOMCAT_INSTALL_PATH: 'C:\Tomcat'
  TOMCAT_HOME_PATH: 'C:\Tomcat\apache-tomcat-$(TOMCAT_VERSION)'
  
  # Download URLs
  JAVA_DOWNLOAD_URL: 'https://github.com/adoptium/temurin17-binaries/releases/download/jdk-17.0.9%2B9/OpenJDK17U-jdk_x64_windows_hotspot_17.0.9_9.zip'
  TOMCAT_DOWNLOAD_URL: 'https://archive.apache.org/dist/tomcat/tomcat-10/v10.1.15/bin/apache-tomcat-10.1.15-windows-x64.zip'

pool:
  vmImage: 'windows-latest'

stages:
  - stage: DownloadPackages
    displayName: 'Download Java and Tomcat'
    jobs:
      - job: DownloadJava
        displayName: 'Download Java JDK'
        condition: eq('${{ parameters.deployJava }}', true)
        steps:
          - task: PowerShell@2
            displayName: 'Download Java JDK'
            inputs:
              targetType: 'inline'
              script: |
                Write-Host "=== Downloading Java JDK ==="
                
                $downloadDir = "$(Pipeline.Workspace)/java-download"
                $javaZip = "$downloadDir/openjdk-17.zip"
                
                New-Item -ItemType Directory -Force -Path $downloadDir | Out-Null
                
                try {
                    Write-Host "Downloading Java from: $(JAVA_DOWNLOAD_URL)"
                    Invoke-WebRequest -Uri "$(JAVA_DOWNLOAD_URL)" -OutFile $javaZip -UseBasicParsing
                    
                    # Verify download
                    if (Test-Path $javaZip) {
                        $fileSize = (Get-Item $javaZip).Length / 1MB
                        Write-Host "✓ Java downloaded successfully ($([math]::Round($fileSize, 1)) MB)"
                        
                        # Extract Java
                        $extractDir = "$(Pipeline.Workspace)/java-staging"
                        New-Item -ItemType Directory -Force -Path $extractDir | Out-Null
                        Expand-Archive -Path $javaZip -DestinationPath $extractDir -Force
                        
                        Write-Host "✓ Java extracted to staging directory"
                    } else {
                        throw "Java download failed - file not found"
                    }
                } catch {
                    Write-Error "Java download failed: $_"
                    exit 1
                }
              pwsh: true

          - task: PublishBuildArtifacts@1
            displayName: 'Publish Java Package'
            inputs:
              pathToPublish: '$(Pipeline.Workspace)/java-staging'
              artifactName: 'java-package'
              publishLocation: 'Container'

      - job: DownloadTomcat
        displayName: 'Download Apache Tomcat'
        condition: eq('${{ parameters.deployTomcat }}', true)
        steps:
          - task: PowerShell@2
            displayName: 'Download Apache Tomcat'
            inputs:
              targetType: 'inline'
              script: |
                Write-Host "=== Downloading Apache Tomcat ==="
                
                $downloadDir = "$(Pipeline.Workspace)/tomcat-download"
                $tomcatZip = "$downloadDir/apache-tomcat-$(TOMCAT_VERSION).zip"
                
                New-Item -ItemType Directory -Force -Path $downloadDir | Out-Null
                
                try {
                    Write-Host "Downloading Tomcat from: $(TOMCAT_DOWNLOAD_URL)"
                    Invoke-WebRequest -Uri "$(TOMCAT_DOWNLOAD_URL)" -OutFile $tomcatZip -UseBasicParsing
                    
                    # Verify download
                    if (Test-Path $tomcatZip) {
                        $fileSize = (Get-Item $tomcatZip).Length / 1MB
                        Write-Host "✓ Tomcat downloaded successfully ($([math]::Round($fileSize, 1)) MB)"
                        
                        # Extract Tomcat
                        $extractDir = "$(Pipeline.Workspace)/tomcat-staging"
                        New-Item -ItemType Directory -Force -Path $extractDir | Out-Null
                        Expand-Archive -Path $tomcatZip -DestinationPath $extractDir -Force
                        
                        Write-Host "✓ Tomcat extracted to staging directory"
                    } else {
                        throw "Tomcat download failed - file not found"
                    }
                } catch {
                    Write-Error "Tomcat download failed: $_"
                    exit 1
                }
              pwsh: true

          - task: PublishBuildArtifacts@1
            displayName: 'Publish Tomcat Package'
            inputs:
              pathToPublish: '$(Pipeline.Workspace)/tomcat-staging'
              artifactName: 'tomcat-package'
              publishLocation: 'Container'

  - stage: DeployApplications
    displayName: 'Deploy Java and Tomcat'
    dependsOn: DownloadPackages
    condition: succeeded()
    jobs:
      - deployment: DeployToServer
        displayName: 'Deploy to Target Server'
        environment: 'windows-servers'
        strategy:
          runOnce:
            deploy:
              steps:
                - download: current
                  artifact: 'java-package'
                  displayName: 'Download Java Package'
                  condition: eq('${{ parameters.deployJava }}', true)

                - download: current
                  artifact: 'tomcat-package'
                  displayName: 'Download Tomcat Package'
                  condition: eq('${{ parameters.deployTomcat }}', true)

                - task: PowerShell@2
                  displayName: 'Pre-Deployment Validation'
                  inputs:
                    targetType: 'inline'
                    script: |
                      Write-Host "=== Pre-Deployment Validation ==="
                      
                      # Check admin privileges
                      $isAdmin = ([Security.Principal.WindowsPrincipal][Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
                      if ($isAdmin) {
                          Write-Host "✓ Running with administrator privileges" -ForegroundColor Green
                      } else {
                          Write-Warning "Not running as administrator - some operations may fail"
                      }
                      
                      # Create directories
                      $dirs = @("$(JAVA_INSTALL_PATH)", "$(TOMCAT_INSTALL_PATH)")
                      foreach ($dir in $dirs) {
                          try {
                              New-Item -ItemType Directory -Path $dir -Force | Out-Null
                              Write-Host "✓ Directory ready: $dir" -ForegroundColor Green
                          } catch {
                              Write-Error "Cannot create directory $dir : $_"
                              exit 1
                          }
                      }
                    pwsh: true

                - task: PowerShell@2
                  displayName: 'Stop Tomcat Services'
                  condition: eq('${{ parameters.deployTomcat }}', true)
                  inputs:
                    targetType: 'inline'
                    script: |
                      Write-Host "=== Stopping Tomcat Services ==="
                      
                      # Stop Tomcat services
                      Get-Service -Name "*tomcat*" -ErrorAction SilentlyContinue | ForEach-Object {
                          if ($_.Status -eq "Running") {
                              Write-Host "Stopping service: $($_.Name)"
                              Stop-Service -Name $_.Name -Force -ErrorAction SilentlyContinue
                          }
                      }
                      
                      # Stop Java processes that might be Tomcat
                      Get-Process -Name "*java*" -ErrorAction SilentlyContinue | Where-Object {
                          $_.ProcessName -like "*tomcat*" -or $_.MainWindowTitle -like "*tomcat*"
                      } | ForEach-Object {
                          Write-Host "Stopping process: $($_.ProcessName) (PID: $($_.Id))"
                          Stop-Process -Id $_.Id -Force -ErrorAction SilentlyContinue
                      }
                      
                      Write-Host "✓ Tomcat services stopped" -ForegroundColor Green
                    pwsh: true
                    continueOnError: true

                - task: PowerShell@2
                  displayName: 'Deploy Java'
                  condition: eq('${{ parameters.deployJava }}', true)
                  inputs:
                    targetType: 'inline'
                    script: |
                      Write-Host "=== Deploying Java ==="
                      
                      $javaHomePath = "$(JAVA_HOME_PATH)"
                      
                      try {
                          # Backup existing installation
                          if ((Test-Path $javaHomePath) -and (-not '${{ parameters.skipBackup }}')) {
                              $backupDir = "$(JAVA_INSTALL_PATH)\backup\java_backup_$(Get-Date -Format 'yyyyMMdd_HHmmss')"
                              Write-Host "Creating backup: $backupDir"
                              New-Item -ItemType Directory -Path (Split-Path $backupDir) -Force | Out-Null
                              Copy-Item -Path $javaHomePath -Destination $backupDir -Recurse -Force
                              Write-Host "✓ Backup created" -ForegroundColor Green
                          }
                          
                          # Find Java in downloaded artifacts
                          $javaPackageDir = "$(Pipeline.Workspace)\java-package"
                          $jdkDirs = Get-ChildItem -Path $javaPackageDir -Directory -Recurse | Where-Object { 
                              $_.Name -like "*jdk*" -or (Test-Path (Join-Path $_.FullName "bin\java.exe"))
                          }
                          
                          if ($jdkDirs.Count -eq 0) {
                              # If no JDK directory found, use the first directory
                              $jdkDirs = Get-ChildItem -Path $javaPackageDir -Directory | Select-Object -First 1
                          }
                          
                          if ($jdkDirs.Count -eq 0) {
                              throw "No Java installation directory found in package"
                          }
                          
                          $sourceJdk = $jdkDirs[0]
                          Write-Host "Installing Java from: $($sourceJdk.FullName)"
                          
                          # Remove existing installation
                          if (Test-Path $javaHomePath) {
                              Remove-Item -Path $javaHomePath -Recurse -Force
                          }
                          
                          # Install new Java
                          Copy-Item -Path $sourceJdk.FullName -Destination $javaHomePath -Recurse -Force
                          
                          # Verify installation
                          $javaExe = Join-Path $javaHomePath "bin\java.exe"
                          if (Test-Path $javaExe) {
                              Write-Host "✓ Java installed successfully" -ForegroundColor Green
                              
                              # Set environment variables
                              try {
                                  [Environment]::SetEnvironmentVariable("JAVA_HOME", $javaHomePath, "Machine")
                                  Write-Host "✓ JAVA_HOME set to: $javaHomePath" -ForegroundColor Green
                              } catch {
                                  Write-Warning "Could not set JAVA_HOME: $_"
                              }
                          } else {
                              throw "Java installation verification failed"
                          }
                          
                      } catch {
                          Write-Error "Java deployment failed: $_"
                          exit 1
                      }
                    pwsh: true

                - task: PowerShell@2
                  displayName: 'Deploy Tomcat'
                  condition: eq('${{ parameters.deployTomcat }}', true)
                  inputs:
                    targetType: 'inline'
                    script: |
                      Write-Host "=== Deploying Tomcat ==="

                      $tomcatHomePath = "$(TOMCAT_HOME_PATH)"

                      try {
                          # Backup existing installation
                          if ((Test-Path $tomcatHomePath) -and (-not '${{ parameters.skipBackup }}')) {
                              $backupDir = "$(TOMCAT_INSTALL_PATH)\backup\tomcat_backup_$(Get-Date -Format 'yyyyMMdd_HHmmss')"
                              Write-Host "Creating backup: $backupDir"
                              New-Item -ItemType Directory -Path (Split-Path $backupDir) -Force | Out-Null
                              Copy-Item -Path $tomcatHomePath -Destination $backupDir -Recurse -Force
                              Write-Host "✓ Backup created" -ForegroundColor Green
                          }

                          # Find Tomcat in downloaded artifacts
                          $tomcatPackageDir = "$(Pipeline.Workspace)\tomcat-package"
                          $tomcatDirs = Get-ChildItem -Path $tomcatPackageDir -Directory -Recurse | Where-Object {
                              $_.Name -like "*tomcat*" -or (Test-Path (Join-Path $_.FullName "bin\catalina.bat"))
                          }

                          if ($tomcatDirs.Count -eq 0) {
                              $tomcatDirs = Get-ChildItem -Path $tomcatPackageDir -Directory | Select-Object -First 1
                          }

                          if ($tomcatDirs.Count -eq 0) {
                              throw "No Tomcat installation directory found in package"
                          }

                          $sourceTomcat = $tomcatDirs[0]
                          Write-Host "Installing Tomcat from: $($sourceTomcat.FullName)"

                          # Remove existing installation
                          if (Test-Path $tomcatHomePath) {
                              Remove-Item -Path $tomcatHomePath -Recurse -Force
                          }

                          # Install new Tomcat
                          Copy-Item -Path $sourceTomcat.FullName -Destination $tomcatHomePath -Recurse -Force

                          # Verify installation
                          $catalinaScript = Join-Path $tomcatHomePath "bin\catalina.bat"
                          if (Test-Path $catalinaScript) {
                              Write-Host "✓ Tomcat installed successfully" -ForegroundColor Green

                              # Set environment variables
                              try {
                                  [Environment]::SetEnvironmentVariable("CATALINA_HOME", $tomcatHomePath, "Machine")
                                  Write-Host "✓ CATALINA_HOME set" -ForegroundColor Green
                              } catch {
                                  Write-Warning "Could not set CATALINA_HOME: $_"
                              }
                          } else {
                              throw "Tomcat installation verification failed"
                          }

                      } catch {
                          Write-Error "Tomcat deployment failed: $_"
                          exit 1
                      }
                    pwsh: true

                - task: PowerShell@2
                  displayName: 'Final Verification'
                  inputs:
                    targetType: 'inline'
                    script: |
                      Write-Host "=== Deployment Complete ==="

                      if ('${{ parameters.deployJava }}' -eq 'true') {
                          $javaExe = "$(JAVA_HOME_PATH)\bin\java.exe"
                          if (Test-Path $javaExe) {
                              Write-Host "✓ Java ready at: $(JAVA_HOME_PATH)" -ForegroundColor Green
                          }
                      }

                      if ('${{ parameters.deployTomcat }}' -eq 'true') {
                          $catalinaScript = "$(TOMCAT_HOME_PATH)\bin\catalina.bat"
                          if (Test-Path $catalinaScript) {
                              Write-Host "✓ Tomcat ready at: $(TOMCAT_HOME_PATH)" -ForegroundColor Green
                          }
                      }

                      Write-Host "Next: Test manually on your server!" -ForegroundColor Yellow
                    pwsh: true
