# Comprehensive Test Script for Java and Tomcat Deployment
# This script tests all aspects of the deployment pipeline

param(
    [Parameter(Mandatory=$false)]
    [string]$JavaHomePath = "C:\Java\jdk-17",
    
    [Parameter(Mandatory=$false)]
    [string]$TomcatHomePath = "C:\Tomcat\apache-tomcat-10.1",
    
    [Parameter(Mandatory=$false)]
    [int]$TomcatPort = 8080,
    
    [Parameter(Mandatory=$false)]
    [int]$TestTimeout = 60
)

Write-Host "=== Java and Tomcat Deployment Test Suite ===" -ForegroundColor Green
Write-Host "Java Home: $JavaHomePath"
Write-Host "Tomcat Home: $TomcatHomePath"
Write-Host "Test Timeout: $TestTimeout seconds"
Write-Host ""

$testResults = @()
$overallSuccess = $true

# Function to add test result
function Add-TestResult {
    param(
        [string]$TestName,
        [bool]$Success,
        [string]$Message,
        [string]$Details = ""
    )
    
    $result = @{
        TestName = $TestName
        Success = $Success
        Message = $Message
        Details = $Details
        Timestamp = Get-Date
    }
    
    $script:testResults += $result
    
    if ($Success) {
        Write-Host "✅ $TestName - $Message" -ForegroundColor Green
    } else {
        Write-Host "❌ $TestName - $Message" -ForegroundColor Red
        $script:overallSuccess = $false
    }
    
    if ($Details) {
        Write-Host "   Details: $Details" -ForegroundColor Gray
    }
}

# Test 1: Java Installation Verification
Write-Host "`n=== Test 1: Java Installation ===" -ForegroundColor Cyan

try {
    if (Test-Path $JavaHomePath) {
        Add-TestResult -TestName "Java Directory" -Success $true -Message "Java installation directory exists" -Details $JavaHomePath
        
        # Test Java executable
        $javaExe = Join-Path $JavaHomePath "bin\java.exe"
        if (Test-Path $javaExe) {
            Add-TestResult -TestName "Java Executable" -Success $true -Message "Java executable found" -Details $javaExe
            
            # Test Java version
            try {
                $versionOutput = & $javaExe -version 2>&1
                $versionLine = $versionOutput | Select-Object -First 1
                Add-TestResult -TestName "Java Version" -Success $true -Message "Java version retrieved" -Details $versionLine
                
                # Test Java compilation
                $testJavaFile = Join-Path $env:TEMP "TestJava.java"
                $testJavaContent = @"
public class TestJava {
    public static void main(String[] args) {
        System.out.println("Java is working correctly!");
        System.out.println("Java version: " + System.getProperty("java.version"));
        System.out.println("Java home: " + System.getProperty("java.home"));
    }
}
"@
                $testJavaContent | Out-File -FilePath $testJavaFile -Encoding ASCII
                
                $javacExe = Join-Path $JavaHomePath "bin\javac.exe"
                if (Test-Path $javacExe) {
                    # Compile test
                    $compileResult = & $javacExe $testJavaFile 2>&1
                    $testClassFile = Join-Path $env:TEMP "TestJava.class"
                    
                    if (Test-Path $testClassFile) {
                        Add-TestResult -TestName "Java Compilation" -Success $true -Message "Java compilation successful"
                        
                        # Run test
                        $runResult = & $javaExe -cp $env:TEMP TestJava 2>&1
                        if ($runResult -like "*Java is working correctly*") {
                            Add-TestResult -TestName "Java Execution" -Success $true -Message "Java execution successful" -Details ($runResult -join "; ")
                        } else {
                            Add-TestResult -TestName "Java Execution" -Success $false -Message "Java execution failed" -Details ($runResult -join "; ")
                        }
                        
                        # Cleanup
                        Remove-Item $testClassFile -ErrorAction SilentlyContinue
                    } else {
                        Add-TestResult -TestName "Java Compilation" -Success $false -Message "Java compilation failed" -Details ($compileResult -join "; ")
                    }
                    
                    Remove-Item $testJavaFile -ErrorAction SilentlyContinue
                } else {
                    Add-TestResult -TestName "Java Compiler" -Success $false -Message "Java compiler (javac) not found" -Details $javacExe
                }
                
            } catch {
                Add-TestResult -TestName "Java Version" -Success $false -Message "Failed to get Java version" -Details $_.Exception.Message
            }
        } else {
            Add-TestResult -TestName "Java Executable" -Success $false -Message "Java executable not found" -Details $javaExe
        }
    } else {
        Add-TestResult -TestName "Java Directory" -Success $false -Message "Java installation directory not found" -Details $JavaHomePath
    }
} catch {
    Add-TestResult -TestName "Java Installation" -Success $false -Message "Java installation test failed" -Details $_.Exception.Message
}

# Test 2: Environment Variables
Write-Host "`n=== Test 2: Environment Variables ===" -ForegroundColor Cyan

try {
    $javaHome = [Environment]::GetEnvironmentVariable("JAVA_HOME", "Machine")
    if ($javaHome) {
        if ($javaHome -eq $JavaHomePath) {
            Add-TestResult -TestName "JAVA_HOME" -Success $true -Message "JAVA_HOME correctly set" -Details $javaHome
        } else {
            Add-TestResult -TestName "JAVA_HOME" -Success $false -Message "JAVA_HOME mismatch" -Details "Expected: $JavaHomePath, Actual: $javaHome"
        }
    } else {
        Add-TestResult -TestName "JAVA_HOME" -Success $false -Message "JAVA_HOME not set"
    }
    
    # Check PATH
    $path = [Environment]::GetEnvironmentVariable("PATH", "Machine")
    $javaBinPath = Join-Path $JavaHomePath "bin"
    if ($path -like "*$javaBinPath*") {
        Add-TestResult -TestName "PATH Variable" -Success $true -Message "Java bin directory in PATH" -Details $javaBinPath
    } else {
        Add-TestResult -TestName "PATH Variable" -Success $false -Message "Java bin directory not in PATH" -Details $javaBinPath
    }
} catch {
    Add-TestResult -TestName "Environment Variables" -Success $false -Message "Environment variable test failed" -Details $_.Exception.Message
}

# Test 3: Tomcat Installation Verification
Write-Host "`n=== Test 3: Tomcat Installation ===" -ForegroundColor Cyan

try {
    if (Test-Path $TomcatHomePath) {
        Add-TestResult -TestName "Tomcat Directory" -Success $true -Message "Tomcat installation directory exists" -Details $TomcatHomePath
        
        # Test Catalina script
        $catalinaScript = Join-Path $TomcatHomePath "bin\catalina.bat"
        if (Test-Path $catalinaScript) {
            Add-TestResult -TestName "Catalina Script" -Success $true -Message "Catalina startup script found" -Details $catalinaScript
            
            # Test Tomcat version
            $releaseNotes = Join-Path $TomcatHomePath "RELEASE-NOTES"
            if (Test-Path $releaseNotes) {
                $versionInfo = Get-Content $releaseNotes -First 10 | Select-String "Apache Tomcat" | Select-Object -First 1
                Add-TestResult -TestName "Tomcat Version" -Success $true -Message "Tomcat version information found" -Details $versionInfo
            } else {
                Add-TestResult -TestName "Tomcat Version" -Success $false -Message "RELEASE-NOTES file not found"
            }
            
            # Test webapps directory
            $webappsDir = Join-Path $TomcatHomePath "webapps"
            if (Test-Path $webappsDir) {
                $webapps = Get-ChildItem $webappsDir
                Add-TestResult -TestName "Webapps Directory" -Success $true -Message "Webapps directory exists with $($webapps.Count) items" -Details $webappsDir
            } else {
                Add-TestResult -TestName "Webapps Directory" -Success $false -Message "Webapps directory not found" -Details $webappsDir
            }
            
        } else {
            Add-TestResult -TestName "Catalina Script" -Success $false -Message "Catalina startup script not found" -Details $catalinaScript
        }
    } else {
        Add-TestResult -TestName "Tomcat Directory" -Success $false -Message "Tomcat installation directory not found" -Details $TomcatHomePath
    }
} catch {
    Add-TestResult -TestName "Tomcat Installation" -Success $false -Message "Tomcat installation test failed" -Details $_.Exception.Message
}

# Test 4: Tomcat Environment Variables
Write-Host "`n=== Test 4: Tomcat Environment Variables ===" -ForegroundColor Cyan

try {
    $catalinaHome = [Environment]::GetEnvironmentVariable("CATALINA_HOME", "Machine")
    if ($catalinaHome) {
        if ($catalinaHome -eq $TomcatHomePath) {
            Add-TestResult -TestName "CATALINA_HOME" -Success $true -Message "CATALINA_HOME correctly set" -Details $catalinaHome
        } else {
            Add-TestResult -TestName "CATALINA_HOME" -Success $false -Message "CATALINA_HOME mismatch" -Details "Expected: $TomcatHomePath, Actual: $catalinaHome"
        }
    } else {
        Add-TestResult -TestName "CATALINA_HOME" -Success $false -Message "CATALINA_HOME not set"
    }
    
    # Check setenv.bat
    $setenvFile = Join-Path $TomcatHomePath "bin\setenv.bat"
    if (Test-Path $setenvFile) {
        $setenvContent = Get-Content $setenvFile -Raw
        if ($setenvContent -like "*JAVA_HOME*") {
            Add-TestResult -TestName "Setenv Configuration" -Success $true -Message "setenv.bat exists and contains JAVA_HOME" -Details $setenvFile
        } else {
            Add-TestResult -TestName "Setenv Configuration" -Success $false -Message "setenv.bat exists but missing JAVA_HOME configuration"
        }
    } else {
        Add-TestResult -TestName "Setenv Configuration" -Success $false -Message "setenv.bat not found" -Details $setenvFile
    }
} catch {
    Add-TestResult -TestName "Tomcat Environment" -Success $false -Message "Tomcat environment test failed" -Details $_.Exception.Message
}

# Test 5: Tomcat Startup Test
Write-Host "`n=== Test 5: Tomcat Startup Test ===" -ForegroundColor Cyan

try {
    # Check if Tomcat is already running
    $existingTomcat = Get-Process -Name "*java*" | Where-Object { $_.CommandLine -like "*catalina*" } -ErrorAction SilentlyContinue
    
    if ($existingTomcat) {
        Add-TestResult -TestName "Tomcat Process" -Success $true -Message "Tomcat is already running" -Details "PID: $($existingTomcat.Id)"
        
        # Test HTTP connection
        try {
            $response = Invoke-WebRequest -Uri "http://localhost:$TomcatPort" -TimeoutSec 10 -UseBasicParsing
            if ($response.StatusCode -eq 200) {
                Add-TestResult -TestName "HTTP Response" -Success $true -Message "Tomcat web server responding" -Details "Status: $($response.StatusCode)"
            } else {
                Add-TestResult -TestName "HTTP Response" -Success $false -Message "Unexpected HTTP status" -Details "Status: $($response.StatusCode)"
            }
        } catch {
            Add-TestResult -TestName "HTTP Response" -Success $false -Message "HTTP connection failed" -Details $_.Exception.Message
        }
    } else {
        # Try to start Tomcat
        $startupScript = Join-Path $TomcatHomePath "bin\startup.bat"
        if (Test-Path $startupScript) {
            Write-Host "   Starting Tomcat for testing..."
            Start-Process -FilePath $startupScript -WorkingDirectory (Join-Path $TomcatHomePath "bin") -WindowStyle Hidden
            
            # Wait for startup
            $startupTimeout = $TestTimeout
            $startupSuccess = $false
            
            for ($i = 0; $i -lt $startupTimeout; $i += 5) {
                Start-Sleep -Seconds 5
                $tomcatProcess = Get-Process -Name "*java*" | Where-Object { $_.CommandLine -like "*catalina*" } -ErrorAction SilentlyContinue
                
                if ($tomcatProcess) {
                    $startupSuccess = $true
                    Add-TestResult -TestName "Tomcat Startup" -Success $true -Message "Tomcat started successfully" -Details "PID: $($tomcatProcess.Id), Time: $($i + 5)s"
                    break
                }
                
                Write-Host "   Waiting for Tomcat startup... ($($i + 5)s/$startupTimeout s)"
            }
            
            if (-not $startupSuccess) {
                Add-TestResult -TestName "Tomcat Startup" -Success $false -Message "Tomcat failed to start within timeout" -Details "$startupTimeout seconds"
            } else {
                # Test HTTP connection after startup
                Start-Sleep -Seconds 5
                try {
                    $response = Invoke-WebRequest -Uri "http://localhost:$TomcatPort" -TimeoutSec 10 -UseBasicParsing
                    if ($response.StatusCode -eq 200) {
                        Add-TestResult -TestName "HTTP Response" -Success $true -Message "Tomcat web server responding after startup" -Details "Status: $($response.StatusCode)"
                    } else {
                        Add-TestResult -TestName "HTTP Response" -Success $false -Message "Unexpected HTTP status after startup" -Details "Status: $($response.StatusCode)"
                    }
                } catch {
                    Add-TestResult -TestName "HTTP Response" -Success $false -Message "HTTP connection failed after startup" -Details $_.Exception.Message
                }
            }
        } else {
            Add-TestResult -TestName "Tomcat Startup" -Success $false -Message "Startup script not found" -Details $startupScript
        }
    }
} catch {
    Add-TestResult -TestName "Tomcat Startup" -Success $false -Message "Tomcat startup test failed" -Details $_.Exception.Message
}

# Generate Test Report
Write-Host "`n=== Test Results Summary ===" -ForegroundColor Green

$successCount = ($testResults | Where-Object { $_.Success }).Count
$totalCount = $testResults.Count
$failureCount = $totalCount - $successCount

Write-Host "Total Tests: $totalCount"
Write-Host "Passed: $successCount" -ForegroundColor Green
Write-Host "Failed: $failureCount" -ForegroundColor Red
Write-Host "Success Rate: $([math]::Round(($successCount / $totalCount) * 100, 1))%"

if ($overallSuccess) {
    Write-Host "`n🎉 ALL TESTS PASSED - Deployment is successful!" -ForegroundColor Green
} else {
    Write-Host "`n⚠️  SOME TESTS FAILED - Review the issues above" -ForegroundColor Yellow
}

# Export detailed results
$reportFile = Join-Path $env:TEMP "deployment-test-report.json"
$testResults | ConvertTo-Json -Depth 3 | Out-File -FilePath $reportFile -Encoding UTF8
Write-Host "`nDetailed test report saved to: $reportFile"

return $overallSuccess
