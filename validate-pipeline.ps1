# Pipeline validation script
# This script validates the Azure pipeline YAML structure and requirements

Write-Host "=== Azure Pipeline Validation ===" -ForegroundColor Green

# Check if pipeline file exists
$pipelineFile = "azure-pipelines.yml"
if (-not (Test-Path $pipelineFile)) {
    Write-Error "Pipeline file not found: $pipelineFile"
    exit 1
}

Write-Host "✓ Pipeline file found: $pipelineFile" -ForegroundColor Green

# Read and validate YAML structure
try {
    $content = Get-Content $pipelineFile -Raw
    Write-Host "✓ Pipeline file readable" -ForegroundColor Green
} catch {
    Write-Error "Failed to read pipeline file: $_"
    exit 1
}

# Check for required sections
$requiredSections = @(
    "trigger:",
    "variables:",
    "pool:",
    "stages:"
)

Write-Host "`n=== Checking Required Sections ===" -ForegroundColor Cyan
foreach ($section in $requiredSections) {
    if ($content -match $section) {
        Write-Host "✓ Found: $section" -ForegroundColor Green
    } else {
        Write-Host "✗ Missing: $section" -ForegroundColor Red
    }
}

# Check for required variables
$requiredVariables = @(
    "JAVA_VERSION",
    "JAVA_INSTALL_PATH",
    "JAVA_HOME_PATH",
    "ARTIFACTS_FEED",
    "ARTIFACTS_PACKAGE"
)

Write-Host "`n=== Checking Required Variables ===" -ForegroundColor Cyan
foreach ($variable in $requiredVariables) {
    if ($content -match $variable) {
        Write-Host "✓ Found variable: $variable" -ForegroundColor Green
    } else {
        Write-Host "✗ Missing variable: $variable" -ForegroundColor Red
    }
}

# Check for required tasks
$requiredTasks = @(
    "UniversalPackages@0",
    "PowerShell@2",
    "PublishBuildArtifacts@1"
)

Write-Host "`n=== Checking Required Tasks ===" -ForegroundColor Cyan
foreach ($task in $requiredTasks) {
    if ($content -match $task) {
        Write-Host "✓ Found task: $task" -ForegroundColor Green
    } else {
        Write-Host "✗ Missing task: $task" -ForegroundColor Red
    }
}

# Check for PowerShell script blocks
$scriptBlocks = ($content | Select-String "script: \|" -AllMatches).Matches.Count
Write-Host "`n=== PowerShell Script Analysis ===" -ForegroundColor Cyan
Write-Host "Found $scriptBlocks PowerShell script blocks"

if ($scriptBlocks -ge 4) {
    Write-Host "✓ Sufficient PowerShell scripts found" -ForegroundColor Green
} else {
    Write-Host "⚠ Expected at least 4 PowerShell script blocks" -ForegroundColor Yellow
}

# Validate YAML syntax (basic check)
Write-Host "`n=== YAML Syntax Validation ===" -ForegroundColor Cyan
$lines = $content -split "`n"
$indentationErrors = 0
$currentIndent = 0

for ($i = 0; $i -lt $lines.Count; $i++) {
    $line = $lines[$i]
    if ($line.Trim() -eq "") { continue }
    
    # Check for consistent indentation (spaces vs tabs)
    if ($line -match "^\t") {
        Write-Host "⚠ Line $($i+1): Uses tabs instead of spaces" -ForegroundColor Yellow
        $indentationErrors++
    }
}

if ($indentationErrors -eq 0) {
    Write-Host "✓ No indentation issues found" -ForegroundColor Green
} else {
    Write-Host "⚠ Found $indentationErrors potential indentation issues" -ForegroundColor Yellow
}

Write-Host "`n=== Validation Complete ===" -ForegroundColor Green
Write-Host "Review any warnings or errors above before running the pipeline."
