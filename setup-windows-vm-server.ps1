# Windows VM Server Setup Script for Azure Pipeline Testing
# Run this script as Administrator on your Windows VM in Parallels

param(
    [string]$AdminPassword = "Pipeline123!",
    [string]$AgentName = "WindowsVM-Agent",
    [switch]$SkipAgentInstall = $false
)

Write-Host "=== Windows VM Server Setup for Azure Pipeline Testing ===" -ForegroundColor Green
Write-Host "This script will configure your Windows VM to act as a test server" -ForegroundColor Yellow

# Check if running as Administrator
$currentPrincipal = New-Object Security.Principal.WindowsPrincipal([Security.Principal.WindowsIdentity]::GetCurrent())
$isAdmin = $currentPrincipal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)

if (-not $isAdmin) {
    Write-Error "This script must be run as Administrator!"
    Write-Host "Right-click PowerShell and select 'Run as Administrator'"
    exit 1
}

Write-Host "✓ Running as Administrator" -ForegroundColor Green

# Phase 1: Basic Configuration
Write-Host "`n=== Phase 1: Basic Configuration ===" -ForegroundColor Cyan

# Set PowerShell execution policy
Write-Host "Setting PowerShell execution policy..."
Set-ExecutionPolicy RemoteSigned -Scope LocalMachine -Force
Write-Host "✓ PowerShell execution policy set to RemoteSigned" -ForegroundColor Green

# Enable PowerShell remoting
Write-Host "Enabling PowerShell remoting..."
try {
    Enable-PSRemoting -Force -SkipNetworkProfileCheck
    Set-Item WSMan:\localhost\Client\TrustedHosts -Value "*" -Force
    Write-Host "✓ PowerShell remoting enabled" -ForegroundColor Green
} catch {
    Write-Warning "PowerShell remoting setup failed: $_"
}

# Create pipeline admin user
Write-Host "Creating pipeline admin user..."
try {
    $securePassword = ConvertTo-SecureString $AdminPassword -AsPlainText -Force
    New-LocalUser -Name "pipelineadmin" -Password $securePassword -Description "Azure Pipeline Admin User" -PasswordNeverExpires -ErrorAction SilentlyContinue
    Add-LocalGroupMember -Group "Administrators" -Member "pipelineadmin" -ErrorAction SilentlyContinue
    Write-Host "✓ Pipeline admin user created (pipelineadmin / $AdminPassword)" -ForegroundColor Green
} catch {
    Write-Warning "User creation failed (may already exist): $_"
}

# Phase 2: Directory Structure
Write-Host "`n=== Phase 2: Directory Structure ===" -ForegroundColor Cyan

$directories = @("C:\Java", "C:\Tomcat", "C:\PipelineAgent", "C:\Logs", "C:\Temp")

foreach ($dir in $directories) {
    Write-Host "Creating directory: $dir"
    New-Item -ItemType Directory -Path $dir -Force | Out-Null
    
    # Set permissions
    try {
        icacls $dir /grant "Everyone:(OI)(CI)F" /T /Q | Out-Null
        Write-Host "✓ Created $dir with full permissions" -ForegroundColor Green
    } catch {
        Write-Warning "Permission setting failed for $dir : $_"
    }
}

# Phase 3: Network Configuration
Write-Host "`n=== Phase 3: Network Configuration ===" -ForegroundColor Cyan

# Configure Windows Firewall
Write-Host "Configuring Windows Firewall..."
$firewallRules = @(
    @{Name="Allow Port 8080"; Port=8080; Description="Tomcat HTTP"},
    @{Name="Allow Port 5985"; Port=5985; Description="PowerShell Remoting"},
    @{Name="Allow Port 22"; Port=22; Description="SSH"}
)

foreach ($rule in $firewallRules) {
    try {
        New-NetFirewallRule -DisplayName $rule.Name -Direction Inbound -Protocol TCP -LocalPort $rule.Port -Action Allow -ErrorAction SilentlyContinue | Out-Null
        Write-Host "✓ Firewall rule added: $($rule.Name)" -ForegroundColor Green
    } catch {
        Write-Warning "Firewall rule failed: $($rule.Name)"
    }
}

# Get network information
$networkInfo = Get-NetIPAddress -AddressFamily IPv4 | Where-Object {$_.IPAddress -notlike "127.*" -and $_.IPAddress -notlike "169.254.*"} | Select-Object -First 1
if ($networkInfo) {
    Write-Host "✓ VM IP Address: $($networkInfo.IPAddress)" -ForegroundColor Green
} else {
    Write-Warning "Could not determine VM IP address"
}

# Phase 4: Install Chocolatey and Tools
Write-Host "`n=== Phase 4: Installing Tools ===" -ForegroundColor Cyan

# Install Chocolatey
Write-Host "Installing Chocolatey package manager..."
try {
    Set-ExecutionPolicy Bypass -Scope Process -Force
    [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072
    Invoke-Expression ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))
    Write-Host "✓ Chocolatey installed" -ForegroundColor Green
    
    # Refresh environment variables
    $env:Path = [System.Environment]::GetEnvironmentVariable("Path","Machine") + ";" + [System.Environment]::GetEnvironmentVariable("Path","User")
    
    # Install useful tools
    Write-Host "Installing useful tools..."
    $tools = @("git", "7zip", "notepadplusplus", "curl")
    foreach ($tool in $tools) {
        try {
            choco install $tool -y --no-progress | Out-Null
            Write-Host "✓ Installed $tool" -ForegroundColor Green
        } catch {
            Write-Warning "Failed to install $tool : $_"
        }
    }
} catch {
    Write-Warning "Chocolatey installation failed: $_"
}

# Phase 5: Azure DevOps Agent (Optional)
if (-not $SkipAgentInstall) {
    Write-Host "`n=== Phase 5: Azure DevOps Agent Setup ===" -ForegroundColor Cyan
    
    Write-Host "Downloading Azure DevOps Agent..."
    try {
        $agentDir = "C:\PipelineAgent"
        Set-Location $agentDir
        
        # Download latest agent
        $agentUrl = "https://vstsagentpackage.azureedge.net/agent/3.232.0/vsts-agent-win-x64-3.232.0.zip"
        Invoke-WebRequest -Uri $agentUrl -OutFile "agent.zip" -UseBasicParsing
        
        # Extract agent
        Expand-Archive -Path "agent.zip" -DestinationPath "." -Force
        Remove-Item "agent.zip"
        
        Write-Host "✓ Azure DevOps Agent downloaded to C:\PipelineAgent" -ForegroundColor Green
        Write-Host "  Next: Run .\config.cmd to configure the agent" -ForegroundColor Yellow
        Write-Host "  You'll need your Azure DevOps URL and Personal Access Token" -ForegroundColor Yellow
    } catch {
        Write-Warning "Agent download failed: $_"
    }
} else {
    Write-Host "Skipping Azure DevOps Agent installation (use -SkipAgentInstall to skip)" -ForegroundColor Yellow
}

# Phase 6: Security Configuration
Write-Host "`n=== Phase 6: Security Configuration ===" -ForegroundColor Cyan

# Configure UAC for testing
Write-Host "Configuring UAC for pipeline testing..."
try {
    Set-ItemProperty -Path "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\System" -Name "ConsentPromptBehaviorAdmin" -Value 0
    Write-Host "✓ UAC configured for admin operations" -ForegroundColor Green
    Write-Host "  Note: Reboot may be required for UAC changes to take effect" -ForegroundColor Yellow
} catch {
    Write-Warning "UAC configuration failed: $_"
}

# Configure Windows Defender exclusions
Write-Host "Adding Windows Defender exclusions..."
$exclusions = @("C:\Java", "C:\Tomcat", "C:\PipelineAgent")
foreach ($exclusion in $exclusions) {
    try {
        Add-MpPreference -ExclusionPath $exclusion -ErrorAction SilentlyContinue
        Write-Host "✓ Added Defender exclusion: $exclusion" -ForegroundColor Green
    } catch {
        Write-Warning "Could not add Defender exclusion for $exclusion"
    }
}

# Phase 7: Create Validation Script
Write-Host "`n=== Phase 7: Creating Validation Script ===" -ForegroundColor Cyan

$validationScript = @'
Write-Host "=== Pipeline Environment Validation ===" -ForegroundColor Green

# Check directories
$dirs = @("C:\Java", "C:\Tomcat", "C:\PipelineAgent")
foreach ($dir in $dirs) {
    if (Test-Path $dir) {
        Write-Host "✓ Directory exists: $dir" -ForegroundColor Green
        try {
            $testFile = Join-Path $dir "test.txt"
            "test" | Out-File -FilePath $testFile
            Remove-Item $testFile
            Write-Host "  ✓ Write permissions OK" -ForegroundColor Green
        } catch {
            Write-Host "  ✗ Write permissions FAILED: $_" -ForegroundColor Red
        }
    } else {
        Write-Host "✗ Directory missing: $dir" -ForegroundColor Red
    }
}

# Check PowerShell
Write-Host "✓ PowerShell Version: $($PSVersionTable.PSVersion)" -ForegroundColor Green
Write-Host "✓ Execution Policy: $(Get-ExecutionPolicy)" -ForegroundColor Green

# Check admin rights
$isAdmin = ([Security.Principal.WindowsPrincipal][Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
if ($isAdmin) {
    Write-Host "✓ Running as Administrator" -ForegroundColor Green
} else {
    Write-Host "✗ NOT running as Administrator" -ForegroundColor Red
}

# Check network
try {
    $response = Invoke-WebRequest -Uri "https://dev.azure.com" -UseBasicParsing -TimeoutSec 10
    Write-Host "✓ Internet connectivity OK" -ForegroundColor Green
} catch {
    Write-Host "✗ Internet connectivity FAILED: $_" -ForegroundColor Red
}

# System info
$computerInfo = Get-ComputerInfo
Write-Host "✓ OS: $($computerInfo.WindowsProductName)" -ForegroundColor Green
Write-Host "✓ RAM: $([math]::Round($computerInfo.TotalPhysicalMemory / 1GB, 1)) GB" -ForegroundColor Green
Write-Host "✓ Free Disk: $([math]::Round((Get-WmiObject -Class Win32_LogicalDisk -Filter "DeviceID='C:'").FreeSpace / 1GB, 1)) GB" -ForegroundColor Green

Write-Host "`n=== Validation Complete ===" -ForegroundColor Green
'@

$validationScript | Out-File -FilePath "C:\validate-pipeline-environment.ps1" -Encoding UTF8
Write-Host "✓ Validation script created: C:\validate-pipeline-environment.ps1" -ForegroundColor Green

# Final Summary
Write-Host "`n=== Setup Complete! ===" -ForegroundColor Green
Write-Host "Your Windows VM is now configured as a test server for Azure Pipelines" -ForegroundColor Yellow
Write-Host ""
Write-Host "Next Steps:" -ForegroundColor Cyan
Write-Host "1. Run validation: PowerShell -File C:\validate-pipeline-environment.ps1" -ForegroundColor White
Write-Host "2. Configure Azure DevOps Agent: cd C:\PipelineAgent && .\config.cmd" -ForegroundColor White
Write-Host "3. Create 'windows-servers' environment in Azure DevOps" -ForegroundColor White
Write-Host "4. Add this VM as a resource to the environment" -ForegroundColor White
Write-Host "5. Test your pipeline!" -ForegroundColor White
Write-Host ""
Write-Host "VM Details:" -ForegroundColor Cyan
Write-Host "- Admin User: pipelineadmin / $AdminPassword" -ForegroundColor White
Write-Host "- IP Address: $($networkInfo.IPAddress)" -ForegroundColor White
Write-Host "- Java Directory: C:\Java" -ForegroundColor White
Write-Host "- Tomcat Directory: C:\Tomcat" -ForegroundColor White
Write-Host ""
Write-Host "Reboot recommended to ensure all changes take effect!" -ForegroundColor Yellow
