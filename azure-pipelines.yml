# Azure DevOps Pipeline for Java Deployment to Windows Server
# This pipeline downloads Java from Azure Artifacts, deploys to Windows server, and manages updates

trigger:
  branches:
    include:
      - main
  paths:
    exclude:
      - README.md
      - docs/*

# Manual trigger for Java updates
pr: none

variables:
  # Java configuration
  JAVA_VERSION: '17'
  JAVA_MAJOR_VERSION: '17'
  JAVA_ARTIFACT_NAME: 'openjdk-$(JAVA_VERSION)'

  # Target server configuration
  TARGET_SERVER: '$(WindowsServerName)'  # Set this in pipeline variables
  JAVA_INSTALL_PATH: 'C:\Java'
  JAVA_HOME_PATH: 'C:\Java\jdk-$(JAVA_VERSION)'

  # Azure Artifacts configuration
  ARTIFACTS_FEED: '$(ArtifactsFeedName)'  # Set this in pipeline variables
  ARTIFACTS_PACKAGE: 'java-jdk'

  # Service connection for target server
  SERVICE_CONNECTION: '$(WindowsServerConnection)'  # Set this in pipeline variables

pool:
  vmImage: 'windows-latest'

stages:
  - stage: CheckVersion
    displayName: 'Check Java Version and Download'
    jobs:
      - job: CheckAndDownload
        displayName: 'Check Current Version and Download Latest'
        steps:
          - task: UniversalPackages@0
            displayName: 'Download Java from Azure Artifacts'
            inputs:
              command: 'download'
              downloadDirectory: '$(Pipeline.Workspace)/java-download'
              feedsToUse: 'internal'
              vstsFeed: '$(ARTIFACTS_FEED)'
              vstsFeedPackage: '$(ARTIFACTS_PACKAGE)'
              vstsPackageVersion: '*'  # Gets latest version
              verbosity: 'Information'

          - task: PowerShell@2
            displayName: 'Extract and Prepare Java Package'
            inputs:
              targetType: 'inline'
              script: |
                # Create staging directory
                $stagingDir = "$(Pipeline.Workspace)/java-staging"
                New-Item -ItemType Directory -Force -Path $stagingDir

                # Find the downloaded Java package (assuming it's a zip or tar.gz)
                $downloadDir = "$(Pipeline.Workspace)/java-download"
                $javaPackage = Get-ChildItem -Path $downloadDir -Recurse -Include "*.zip", "*.tar.gz" | Select-Object -First 1

                if ($javaPackage) {
                    Write-Host "Found Java package: $($javaPackage.FullName)"

                    # Extract the package
                    if ($javaPackage.Extension -eq ".zip") {
                        Expand-Archive -Path $javaPackage.FullName -DestinationPath $stagingDir -Force
                    } else {
                        # For tar.gz files, you might need 7-Zip or similar
                        Write-Host "Extracting tar.gz file..."
                        # Add extraction logic for tar.gz if needed
                    }

                    # Set variable for next stage
                    Write-Host "##vso[task.setvariable variable=JavaPackageReady;isOutput=true]true"
                    Write-Host "##vso[task.setvariable variable=JavaStagingPath;isOutput=true]$stagingDir"
                } else {
                    Write-Error "No Java package found in download directory"
                    Write-Host "##vso[task.setvariable variable=JavaPackageReady;isOutput=true]false"
                }
              pwsh: true

          - task: PublishBuildArtifacts@1
            displayName: 'Publish Java Package'
            inputs:
              pathToPublish: '$(Pipeline.Workspace)/java-staging'
              artifactName: 'java-package'
              publishLocation: 'Container'

  - stage: DeployJava
    displayName: 'Deploy Java to Windows Server'
    dependsOn: CheckVersion
    condition: succeeded()
    jobs:
      - deployment: DeployToServer
        displayName: 'Deploy Java to Target Server'
        environment: 'windows-servers'
        strategy:
          runOnce:
            deploy:
              steps:
                - download: current
                  artifact: 'java-package'
                  displayName: 'Download Java Package'

                - task: PowerShell@2
                  displayName: 'Check Current Java Installation'
                  inputs:
                    targetType: 'inline'
                    script: |
                      # Check if Java is already installed and get version
                      $currentJavaPath = "$(JAVA_HOME_PATH)"
                      $backupPath = "$(JAVA_INSTALL_PATH)\backup"

                      if (Test-Path $currentJavaPath) {
                          Write-Host "Current Java installation found at: $currentJavaPath"

                          # Get current Java version
                          try {
                              $javaExe = Join-Path $currentJavaPath "bin\java.exe"
                              if (Test-Path $javaExe) {
                                  $currentVersion = & $javaExe -version 2>&1 | Select-String "version" | Select-Object -First 1
                                  Write-Host "Current Java version: $currentVersion"

                                  # Create backup directory
                                  New-Item -ItemType Directory -Force -Path $backupPath

                                  # Backup current installation
                                  $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
                                  $backupDir = Join-Path $backupPath "java_backup_$timestamp"
                                  Write-Host "Creating backup at: $backupDir"
                                  Copy-Item -Path $currentJavaPath -Destination $backupDir -Recurse -Force

                                  Write-Host "##vso[task.setvariable variable=JavaBackupCreated]true"
                                  Write-Host "##vso[task.setvariable variable=JavaBackupPath]$backupDir"
                              }
                          } catch {
                              Write-Warning "Could not determine current Java version: $_"
                          }
                      } else {
                          Write-Host "No existing Java installation found at: $currentJavaPath"
                          Write-Host "##vso[task.setvariable variable=JavaBackupCreated]false"
                      }
                    pwsh: true

                - task: PowerShell@2
                  displayName: 'Install/Update Java'
                  inputs:
                    targetType: 'inline'
                    script: |
                      # Create Java installation directory
                      $installPath = "$(JAVA_INSTALL_PATH)"
                      $javaHomePath = "$(JAVA_HOME_PATH)"

                      New-Item -ItemType Directory -Force -Path $installPath

                      # Remove existing installation if it exists
                      if (Test-Path $javaHomePath) {
                          Write-Host "Removing existing Java installation..."
                          Remove-Item -Path $javaHomePath -Recurse -Force
                      }

                      # Find the extracted Java directory
                      $downloadedJava = Get-ChildItem -Path "$(Pipeline.Workspace)\java-package" -Directory | Select-Object -First 1

                      if ($downloadedJava) {
                          Write-Host "Installing Java from: $($downloadedJava.FullName)"

                          # Copy Java to installation directory
                          Copy-Item -Path $downloadedJava.FullName -Destination $javaHomePath -Recurse -Force

                          # Verify installation
                          $javaExe = Join-Path $javaHomePath "bin\java.exe"
                          if (Test-Path $javaExe) {
                              Write-Host "Java installation successful!"

                              # Get new version
                              $newVersion = & $javaExe -version 2>&1 | Select-String "version" | Select-Object -First 1
                              Write-Host "New Java version: $newVersion"

                              Write-Host "##vso[task.setvariable variable=JavaInstallSuccess]true"
                          } else {
                              Write-Error "Java installation failed - java.exe not found"
                              Write-Host "##vso[task.setvariable variable=JavaInstallSuccess]false"
                          }
                      } else {
                          Write-Error "No Java directory found in downloaded package"
                          Write-Host "##vso[task.setvariable variable=JavaInstallSuccess]false"
                      }
                    pwsh: true

                - task: PowerShell@2
                  displayName: 'Update Environment Variables'
                  condition: eq(variables['JavaInstallSuccess'], 'true')
                  inputs:
                    targetType: 'inline'
                    script: |
                      # Update JAVA_HOME environment variable
                      $javaHomePath = "$(JAVA_HOME_PATH)"

                      Write-Host "Setting JAVA_HOME to: $javaHomePath"

                      # Set system environment variable
                      [Environment]::SetEnvironmentVariable("JAVA_HOME", $javaHomePath, "Machine")

                      # Update PATH to include Java bin directory
                      $javaBinPath = Join-Path $javaHomePath "bin"
                      $currentPath = [Environment]::GetEnvironmentVariable("PATH", "Machine")

                      # Remove any existing Java paths from PATH
                      $pathEntries = $currentPath -split ";" | Where-Object { $_ -notlike "*Java*" -and $_ -notlike "*jdk*" -and $_ -notlike "*jre*" }

                      # Add new Java bin path
                      $newPath = ($pathEntries + $javaBinPath) -join ";"
                      [Environment]::SetEnvironmentVariable("PATH", $newPath, "Machine")

                      Write-Host "Environment variables updated successfully"
                      Write-Host "JAVA_HOME: $javaHomePath"
                      Write-Host "Java bin added to PATH: $javaBinPath"
                    pwsh: true

                - task: PowerShell@2
                  displayName: 'Verify Installation and Cleanup'
                  inputs:
                    targetType: 'inline'
                    script: |
                      # Verify the installation
                      $javaExe = "$(JAVA_HOME_PATH)\bin\java.exe"

                      if (Test-Path $javaExe) {
                          Write-Host "=== Java Installation Verification ==="
                          & $javaExe -version

                          Write-Host "`n=== Environment Variables ==="
                          Write-Host "JAVA_HOME: $env:JAVA_HOME"

                          # Clean up old backups (keep only last 3)
                          $backupPath = "$(JAVA_INSTALL_PATH)\backup"
                          if (Test-Path $backupPath) {
                              $backups = Get-ChildItem -Path $backupPath -Directory | Sort-Object CreationTime -Descending
                              if ($backups.Count -gt 3) {
                                  $backupsToRemove = $backups | Select-Object -Skip 3
                                  foreach ($backup in $backupsToRemove) {
                                      Write-Host "Removing old backup: $($backup.Name)"
                                      Remove-Item -Path $backup.FullName -Recurse -Force
                                  }
                              }
                          }

                          Write-Host "`n=== Java Deployment Completed Successfully ==="
                      } else {
                          Write-Error "Java installation verification failed"
                          exit 1
                      }
                    pwsh: true
