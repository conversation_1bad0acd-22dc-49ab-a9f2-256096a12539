# Azure DevOps Pipeline for Spring Boot Demo Application
# This pipeline builds, tests, and packages a Spring Boot application

trigger:
  branches:
    include:
      - main
      - develop
      - feature/*
  paths:
    exclude:
      - README.md
      - docs/*

pr:
  branches:
    include:
      - main
      - develop

variables:
  # Maven configuration
  MAVEN_CACHE_FOLDER: $(Pipeline.Workspace)/.m2/repository
  MAVEN_OPTS: '-Dmaven.repo.local=$(MAVEN_CACHE_FOLDER)'
  
  # Java configuration
  JAVA_VERSION: '17'
  
  # Application configuration
  APP_NAME: 'spring-boot-demo'
  ARTIFACT_NAME: 'demo-0.0.1-SNAPSHOT.jar'

pool:
  vmImage: 'ubuntu-latest'

stages:
  - stage: Build
    displayName: 'Build and Test'
    jobs:
      - job: BuildAndTest
        displayName: 'Build and Test Application'
        steps:
          - task: JavaToolInstaller@0
            displayName: 'Set up JDK $(JAVA_VERSION)'
            inputs:
              versionSpec: '$(JAVA_VERSION)'
              jdkArchitectureOption: 'x64'
              jdkSourceOption: 'PreInstalled'

          - task: Cache@2
            displayName: 'Cache Maven dependencies'
            inputs:
              key: 'maven | "$(Agent.OS)" | **/pom.xml'
              restoreKeys: |
                maven | "$(Agent.OS)"
                maven
              path: $(MAVEN_CACHE_FOLDER)

          - task: Maven@3
            displayName: 'Maven Clean'
            inputs:
              mavenPomFile: 'pom.xml'
              goals: 'clean'
              options: '$(MAVEN_OPTS)'
              javaHomeOption: 'JDKVersion'
              jdkVersionOption: '$(JAVA_VERSION)'
              mavenVersionOption: 'Default'
              publishJUnitResults: false

          - task: Maven@3
            displayName: 'Maven Compile'
            inputs:
              mavenPomFile: 'pom.xml'
              goals: 'compile'
              options: '$(MAVEN_OPTS)'
              javaHomeOption: 'JDKVersion'
              jdkVersionOption: '$(JAVA_VERSION)'
              mavenVersionOption: 'Default'
              publishJUnitResults: false

          - task: Maven@3
            displayName: 'Maven Test'
            inputs:
              mavenPomFile: 'pom.xml'
              goals: 'test'
              options: '$(MAVEN_OPTS)'
              javaHomeOption: 'JDKVersion'
              jdkVersionOption: '$(JAVA_VERSION)'
              mavenVersionOption: 'Default'
              publishJUnitResults: true
              testResultsFiles: '**/surefire-reports/TEST-*.xml'
              codeCoverageToolOption: 'JaCoCo'

          - task: Maven@3
            displayName: 'Maven Package'
            inputs:
              mavenPomFile: 'pom.xml'
              goals: 'package'
              options: '$(MAVEN_OPTS) -DskipTests'
              javaHomeOption: 'JDKVersion'
              jdkVersionOption: '$(JAVA_VERSION)'
              mavenVersionOption: 'Default'
              publishJUnitResults: false

          - task: CopyFiles@2
            displayName: 'Copy JAR file to staging directory'
            inputs:
              sourceFolder: '$(System.DefaultWorkingDirectory)/target'
              contents: '*.jar'
              targetFolder: '$(Build.ArtifactStagingDirectory)'

          - task: PublishBuildArtifacts@1
            displayName: 'Publish Build Artifacts'
            inputs:
              pathToPublish: '$(Build.ArtifactStagingDirectory)'
              artifactName: '$(APP_NAME)-artifacts'
              publishLocation: 'Container'

  - stage: CodeQuality
    displayName: 'Code Quality Analysis'
    dependsOn: Build
    condition: succeeded()
    jobs:
      - job: SonarQube
        displayName: 'SonarQube Analysis'
        steps:
          - task: JavaToolInstaller@0
            displayName: 'Set up JDK $(JAVA_VERSION)'
            inputs:
              versionSpec: '$(JAVA_VERSION)'
              jdkArchitectureOption: 'x64'
              jdkSourceOption: 'PreInstalled'

          - task: Cache@2
            displayName: 'Cache Maven dependencies'
            inputs:
              key: 'maven | "$(Agent.OS)" | **/pom.xml'
              restoreKeys: |
                maven | "$(Agent.OS)"
                maven
              path: $(MAVEN_CACHE_FOLDER)

          # Uncomment and configure the following task if you have SonarQube set up
          # - task: SonarQubePrepare@4
          #   displayName: 'Prepare SonarQube analysis'
          #   inputs:
          #     SonarQube: 'YourSonarQubeServiceConnection'
          #     scannerMode: 'Other'

          # - task: Maven@3
          #   displayName: 'Run SonarQube analysis'
          #   inputs:
          #     mavenPomFile: 'pom.xml'
          #     goals: 'sonar:sonar'
          #     options: '$(MAVEN_OPTS)'
          #     javaHomeOption: 'JDKVersion'
          #     jdkVersionOption: '$(JAVA_VERSION)'

          # - task: SonarQubePublish@4
          #   displayName: 'Publish SonarQube results'

          - script: |
              echo "Code quality analysis placeholder"
              echo "Configure SonarQube tasks above for actual code quality analysis"
            displayName: 'Code Quality Placeholder'

  - stage: Deploy
    displayName: 'Deploy to Development'
    dependsOn: 
      - Build
      - CodeQuality
    condition: and(succeeded(), eq(variables['Build.SourceBranch'], 'refs/heads/main'))
    jobs:
      - deployment: DeployToDev
        displayName: 'Deploy to Development Environment'
        environment: 'development'
        strategy:
          runOnce:
            deploy:
              steps:
                - download: current
                  artifact: '$(APP_NAME)-artifacts'
                  displayName: 'Download Build Artifacts'

                - script: |
                    echo "Deploying $(ARTIFACT_NAME) to development environment"
                    echo "Artifact location: $(Pipeline.Workspace)/$(APP_NAME)-artifacts/$(ARTIFACT_NAME)"
                    # Add your deployment commands here
                    # For example:
                    # - Copy JAR to application server
                    # - Restart application service
                    # - Run health checks
                  displayName: 'Deploy Application'

                - script: |
                    echo "Running post-deployment health checks"
                    # Add health check commands here
                    # curl -f http://your-app-url/actuator/health || exit 1
                  displayName: 'Health Check'
