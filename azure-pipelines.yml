# Azure DevOps Pipeline for Java Deployment to Windows Server
# This pipeline downloads Java from Azure Artifacts, deploys to Windows server, and manages updates

trigger:
  branches:
    include:
      - main
  paths:
    exclude:
      - README.md
      - docs/*

# Manual trigger for Java updates
pr: none

variables:
  # Java configuration
  JAVA_VERSION: '17'
  JAVA_MAJOR_VERSION: '17'
  JAVA_ARTIFACT_NAME: 'openjdk-$(JAVA_VERSION)'

  # Tomcat configuration
  TOMCAT_VERSION: '10.1'
  TOMCAT_MAJOR_VERSION: '10'
  TOMCAT_ARTIFACT_NAME: 'apache-tomcat-$(TOMCAT_VERSION)'

  # Target server configuration
  TARGET_SERVER: '$(WindowsServerName)'  # Set this in pipeline variables
  JAVA_INSTALL_PATH: 'C:\Java'
  JAVA_HOME_PATH: 'C:\Java\jdk-$(JAVA_VERSION)'
  TOMCAT_INSTALL_PATH: 'C:\Tomcat'
  TOMCAT_HOME_PATH: 'C:\Tomcat\apache-tomcat-$(TOMCAT_VERSION)'

  # Azure Artifacts configuration
  ARTIFACTS_FEED: '$(ArtifactsFeedName)'  # Set this in pipeline variables
  JAVA_ARTIFACTS_PACKAGE: 'java-jdk'
  TOMCAT_ARTIFACTS_PACKAGE: 'apache-tomcat'

  # Service connection for target server
  SERVICE_CONNECTION: '$(WindowsServerConnection)'  # Set this in pipeline variables

pool:
  vmImage: 'windows-latest'

stages:
  - stage: CheckVersionAndDownload
    displayName: 'Check Versions and Download Packages'
    jobs:
      - job: CheckAndDownloadJava
        displayName: 'Check Java Version and Download Latest'
        steps:
          - task: UniversalPackages@0
            displayName: 'Download Java from Azure Artifacts'
            inputs:
              command: 'download'
              downloadDirectory: '$(Pipeline.Workspace)/java-download'
              feedsToUse: 'internal'
              vstsFeed: '$(ARTIFACTS_FEED)'
              vstsFeedPackage: '$(JAVA_ARTIFACTS_PACKAGE)'
              vstsPackageVersion: '*'  # Gets latest version
              verbosity: 'Information'

          - task: PowerShell@2
            displayName: 'Extract and Prepare Java Package'
            inputs:
              targetType: 'inline'
              script: |
                # Create staging directory
                $stagingDir = "$(Pipeline.Workspace)/java-staging"
                New-Item -ItemType Directory -Force -Path $stagingDir

                # Find the downloaded Java package (assuming it's a zip or tar.gz)
                $downloadDir = "$(Pipeline.Workspace)/java-download"
                $javaPackage = Get-ChildItem -Path $downloadDir -Recurse -Include "*.zip", "*.tar.gz" | Select-Object -First 1

                if ($javaPackage) {
                    Write-Host "Found Java package: $($javaPackage.FullName)"

                    # Extract the package
                    if ($javaPackage.Extension -eq ".zip") {
                        Expand-Archive -Path $javaPackage.FullName -DestinationPath $stagingDir -Force
                    } else {
                        # For tar.gz files, you might need 7-Zip or similar
                        Write-Host "Extracting tar.gz file..."
                        # Add extraction logic for tar.gz if needed
                    }

                    # Set variable for next stage
                    Write-Host "##vso[task.setvariable variable=JavaPackageReady;isOutput=true]true"
                    Write-Host "##vso[task.setvariable variable=JavaStagingPath;isOutput=true]$stagingDir"
                } else {
                    Write-Error "No Java package found in download directory"
                    Write-Host "##vso[task.setvariable variable=JavaPackageReady;isOutput=true]false"
                }
              pwsh: true

          - task: PublishBuildArtifacts@1
            displayName: 'Publish Java Package'
            inputs:
              pathToPublish: '$(Pipeline.Workspace)/java-staging'
              artifactName: 'java-package'
              publishLocation: 'Container'

      - job: CheckAndDownloadTomcat
        displayName: 'Check Tomcat Version and Download Latest'
        steps:
          - task: UniversalPackages@0
            displayName: 'Download Tomcat from Azure Artifacts'
            inputs:
              command: 'download'
              downloadDirectory: '$(Pipeline.Workspace)/tomcat-download'
              feedsToUse: 'internal'
              vstsFeed: '$(ARTIFACTS_FEED)'
              vstsFeedPackage: '$(TOMCAT_ARTIFACTS_PACKAGE)'
              vstsPackageVersion: '*'  # Gets latest version
              verbosity: 'Information'

          - task: PowerShell@2
            displayName: 'Extract and Prepare Tomcat Package'
            inputs:
              targetType: 'inline'
              script: |
                # Create staging directory
                $stagingDir = "$(Pipeline.Workspace)/tomcat-staging"
                New-Item -ItemType Directory -Force -Path $stagingDir

                # Find the downloaded Tomcat package
                $downloadDir = "$(Pipeline.Workspace)/tomcat-download"
                $tomcatPackage = Get-ChildItem -Path $downloadDir -Recurse -Include "*.zip", "*.tar.gz" | Select-Object -First 1

                if ($tomcatPackage) {
                    Write-Host "Found Tomcat package: $($tomcatPackage.FullName)"

                    # Extract the package
                    if ($tomcatPackage.Extension -eq ".zip") {
                        Expand-Archive -Path $tomcatPackage.FullName -DestinationPath $stagingDir -Force
                    } else {
                        # For tar.gz files, you might need 7-Zip or similar
                        Write-Host "Extracting tar.gz file..."
                        # Add extraction logic for tar.gz if needed
                    }

                    # Set variable for next stage
                    Write-Host "##vso[task.setvariable variable=TomcatPackageReady;isOutput=true]true"
                    Write-Host "##vso[task.setvariable variable=TomcatStagingPath;isOutput=true]$stagingDir"
                } else {
                    Write-Error "No Tomcat package found in download directory"
                    Write-Host "##vso[task.setvariable variable=TomcatPackageReady;isOutput=true]false"
                }
              pwsh: true

          - task: PublishBuildArtifacts@1
            displayName: 'Publish Tomcat Package'
            inputs:
              pathToPublish: '$(Pipeline.Workspace)/tomcat-staging'
              artifactName: 'tomcat-package'
              publishLocation: 'Container'

  - stage: DeployJavaAndTomcat
    displayName: 'Deploy Java and Tomcat to Windows Server'
    dependsOn: CheckVersionAndDownload
    condition: succeeded()
    jobs:
      - deployment: DeployToServer
        displayName: 'Deploy Java and Tomcat to Target Server'
        environment: 'windows-servers'
        strategy:
          runOnce:
            deploy:
              steps:
                - download: current
                  artifact: 'java-package'
                  displayName: 'Download Java Package'

                - download: current
                  artifact: 'tomcat-package'
                  displayName: 'Download Tomcat Package'

                - task: PowerShell@2
                  displayName: 'Stop Tomcat Service (if running)'
                  inputs:
                    targetType: 'inline'
                    script: |
                      # Stop Tomcat service if it exists and is running
                      $tomcatService = Get-Service -Name "Tomcat*" -ErrorAction SilentlyContinue

                      if ($tomcatService) {
                          foreach ($service in $tomcatService) {
                              if ($service.Status -eq "Running") {
                                  Write-Host "Stopping Tomcat service: $($service.Name)"
                                  Stop-Service -Name $service.Name -Force
                                  Write-Host "Waiting for service to stop..."
                                  $service.WaitForStatus("Stopped", "00:02:00")
                                  Write-Host "Service stopped successfully"
                              } else {
                                  Write-Host "Tomcat service $($service.Name) is already stopped"
                              }
                          }
                      } else {
                          Write-Host "No Tomcat service found - this may be a new installation"
                      }

                      # Also try to stop any running Tomcat processes
                      $tomcatProcesses = Get-Process -Name "*tomcat*", "*catalina*" -ErrorAction SilentlyContinue
                      if ($tomcatProcesses) {
                          Write-Host "Found running Tomcat processes, stopping them..."
                          $tomcatProcesses | Stop-Process -Force
                          Write-Host "Tomcat processes stopped"
                      }
                    pwsh: true

                - task: PowerShell@2
                  displayName: 'Check Current Java Installation'
                  inputs:
                    targetType: 'inline'
                    script: |
                      # Check if Java is already installed and get version
                      $currentJavaPath = "$(JAVA_HOME_PATH)"
                      $backupPath = "$(JAVA_INSTALL_PATH)\backup"

                      if (Test-Path $currentJavaPath) {
                          Write-Host "Current Java installation found at: $currentJavaPath"

                          # Get current Java version
                          try {
                              $javaExe = Join-Path $currentJavaPath "bin\java.exe"
                              if (Test-Path $javaExe) {
                                  $currentVersion = & $javaExe -version 2>&1 | Select-String "version" | Select-Object -First 1
                                  Write-Host "Current Java version: $currentVersion"

                                  # Create backup directory
                                  New-Item -ItemType Directory -Force -Path $backupPath

                                  # Backup current installation
                                  $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
                                  $backupDir = Join-Path $backupPath "java_backup_$timestamp"
                                  Write-Host "Creating backup at: $backupDir"
                                  Copy-Item -Path $currentJavaPath -Destination $backupDir -Recurse -Force

                                  Write-Host "##vso[task.setvariable variable=JavaBackupCreated]true"
                                  Write-Host "##vso[task.setvariable variable=JavaBackupPath]$backupDir"
                              }
                          } catch {
                              Write-Warning "Could not determine current Java version: $_"
                          }
                      } else {
                          Write-Host "No existing Java installation found at: $currentJavaPath"
                          Write-Host "##vso[task.setvariable variable=JavaBackupCreated]false"
                      }
                    pwsh: true

                - task: PowerShell@2
                  displayName: 'Install/Update Java'
                  inputs:
                    targetType: 'inline'
                    script: |
                      # Create Java installation directory
                      $installPath = "$(JAVA_INSTALL_PATH)"
                      $javaHomePath = "$(JAVA_HOME_PATH)"

                      New-Item -ItemType Directory -Force -Path $installPath

                      # Remove existing installation if it exists
                      if (Test-Path $javaHomePath) {
                          Write-Host "Removing existing Java installation..."
                          Remove-Item -Path $javaHomePath -Recurse -Force
                      }

                      # Find the extracted Java directory
                      $downloadedJava = Get-ChildItem -Path "$(Pipeline.Workspace)\java-package" -Directory | Select-Object -First 1

                      if ($downloadedJava) {
                          Write-Host "Installing Java from: $($downloadedJava.FullName)"

                          # Copy Java to installation directory
                          Copy-Item -Path $downloadedJava.FullName -Destination $javaHomePath -Recurse -Force

                          # Verify installation
                          $javaExe = Join-Path $javaHomePath "bin\java.exe"
                          if (Test-Path $javaExe) {
                              Write-Host "Java installation successful!"

                              # Get new version
                              $newVersion = & $javaExe -version 2>&1 | Select-String "version" | Select-Object -First 1
                              Write-Host "New Java version: $newVersion"

                              Write-Host "##vso[task.setvariable variable=JavaInstallSuccess]true"
                          } else {
                              Write-Error "Java installation failed - java.exe not found"
                              Write-Host "##vso[task.setvariable variable=JavaInstallSuccess]false"
                          }
                      } else {
                          Write-Error "No Java directory found in downloaded package"
                          Write-Host "##vso[task.setvariable variable=JavaInstallSuccess]false"
                      }
                    pwsh: true

                - task: PowerShell@2
                  displayName: 'Update Environment Variables'
                  condition: eq(variables['JavaInstallSuccess'], 'true')
                  inputs:
                    targetType: 'inline'
                    script: |
                      # Update JAVA_HOME environment variable
                      $javaHomePath = "$(JAVA_HOME_PATH)"

                      Write-Host "Setting JAVA_HOME to: $javaHomePath"

                      # Set system environment variable
                      [Environment]::SetEnvironmentVariable("JAVA_HOME", $javaHomePath, "Machine")

                      # Update PATH to include Java bin directory
                      $javaBinPath = Join-Path $javaHomePath "bin"
                      $currentPath = [Environment]::GetEnvironmentVariable("PATH", "Machine")

                      # Remove any existing Java paths from PATH
                      $pathEntries = $currentPath -split ";" | Where-Object { $_ -notlike "*Java*" -and $_ -notlike "*jdk*" -and $_ -notlike "*jre*" }

                      # Add new Java bin path
                      $newPath = ($pathEntries + $javaBinPath) -join ";"
                      [Environment]::SetEnvironmentVariable("PATH", $newPath, "Machine")

                      Write-Host "Environment variables updated successfully"
                      Write-Host "JAVA_HOME: $javaHomePath"
                      Write-Host "Java bin added to PATH: $javaBinPath"
                    pwsh: true

                - task: PowerShell@2
                  displayName: 'Verify Installation and Cleanup'
                  inputs:
                    targetType: 'inline'
                    script: |
                      # Verify the installation
                      $javaExe = "$(JAVA_HOME_PATH)\bin\java.exe"

                      if (Test-Path $javaExe) {
                          Write-Host "=== Java Installation Verification ==="
                          & $javaExe -version

                          Write-Host "`n=== Environment Variables ==="
                          Write-Host "JAVA_HOME: $env:JAVA_HOME"

                          # Clean up old backups (keep only last 3)
                          $backupPath = "$(JAVA_INSTALL_PATH)\backup"
                          if (Test-Path $backupPath) {
                              $backups = Get-ChildItem -Path $backupPath -Directory | Sort-Object CreationTime -Descending
                              if ($backups.Count -gt 3) {
                                  $backupsToRemove = $backups | Select-Object -Skip 3
                                  foreach ($backup in $backupsToRemove) {
                                      Write-Host "Removing old backup: $($backup.Name)"
                                      Remove-Item -Path $backup.FullName -Recurse -Force
                                  }
                              }
                          }

                          Write-Host "`n=== Java Deployment Completed Successfully ==="
                      } else {
                          Write-Error "Java installation verification failed"
                          exit 1
                      }
                    pwsh: true

                - task: PowerShell@2
                  displayName: 'Check Current Tomcat Installation'
                  inputs:
                    targetType: 'inline'
                    script: |
                      # Check if Tomcat is already installed and get version
                      $currentTomcatPath = "$(TOMCAT_HOME_PATH)"
                      $backupPath = "$(TOMCAT_INSTALL_PATH)\backup"

                      if (Test-Path $currentTomcatPath) {
                          Write-Host "Current Tomcat installation found at: $currentTomcatPath"

                          # Get current Tomcat version
                          try {
                              $versionFile = Join-Path $currentTomcatPath "RELEASE-NOTES"
                              if (Test-Path $versionFile) {
                                  $versionInfo = Get-Content $versionFile -First 10 | Select-String "Apache Tomcat" | Select-Object -First 1
                                  Write-Host "Current Tomcat version: $versionInfo"
                              }

                              # Create backup directory
                              New-Item -ItemType Directory -Force -Path $backupPath

                              # Backup current installation
                              $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
                              $backupDir = Join-Path $backupPath "tomcat_backup_$timestamp"
                              Write-Host "Creating backup at: $backupDir"
                              Copy-Item -Path $currentTomcatPath -Destination $backupDir -Recurse -Force

                              Write-Host "##vso[task.setvariable variable=TomcatBackupCreated]true"
                              Write-Host "##vso[task.setvariable variable=TomcatBackupPath]$backupDir"
                          } catch {
                              Write-Warning "Could not determine current Tomcat version: $_"
                          }
                      } else {
                          Write-Host "No existing Tomcat installation found at: $currentTomcatPath"
                          Write-Host "##vso[task.setvariable variable=TomcatBackupCreated]false"
                      }
                    pwsh: true

                - task: PowerShell@2
                  displayName: 'Install/Update Tomcat'
                  inputs:
                    targetType: 'inline'
                    script: |
                      # Create Tomcat installation directory
                      $installPath = "$(TOMCAT_INSTALL_PATH)"
                      $tomcatHomePath = "$(TOMCAT_HOME_PATH)"

                      New-Item -ItemType Directory -Force -Path $installPath

                      # Remove existing installation if it exists
                      if (Test-Path $tomcatHomePath) {
                          Write-Host "Removing existing Tomcat installation..."
                          Remove-Item -Path $tomcatHomePath -Recurse -Force
                      }

                      # Find the extracted Tomcat directory
                      $downloadedTomcat = Get-ChildItem -Path "$(Pipeline.Workspace)\tomcat-package" -Directory | Select-Object -First 1

                      if ($downloadedTomcat) {
                          Write-Host "Installing Tomcat from: $($downloadedTomcat.FullName)"

                          # Copy Tomcat to installation directory
                          Copy-Item -Path $downloadedTomcat.FullName -Destination $tomcatHomePath -Recurse -Force

                          # Verify installation
                          $catalinaScript = Join-Path $tomcatHomePath "bin\catalina.bat"
                          if (Test-Path $catalinaScript) {
                              Write-Host "Tomcat installation successful!"

                              # Get new version from RELEASE-NOTES
                              $versionFile = Join-Path $tomcatHomePath "RELEASE-NOTES"
                              if (Test-Path $versionFile) {
                                  $versionInfo = Get-Content $versionFile -First 10 | Select-String "Apache Tomcat" | Select-Object -First 1
                                  Write-Host "New Tomcat version: $versionInfo"
                              }

                              Write-Host "##vso[task.setvariable variable=TomcatInstallSuccess]true"
                          } else {
                              Write-Error "Tomcat installation failed - catalina.bat not found"
                              Write-Host "##vso[task.setvariable variable=TomcatInstallSuccess]false"
                          }
                      } else {
                          Write-Error "No Tomcat directory found in downloaded package"
                          Write-Host "##vso[task.setvariable variable=TomcatInstallSuccess]false"
                      }
                    pwsh: true

                - task: PowerShell@2
                  displayName: 'Configure Tomcat Environment'
                  condition: eq(variables['TomcatInstallSuccess'], 'true')
                  inputs:
                    targetType: 'inline'
                    script: |
                      # Update CATALINA_HOME environment variable
                      $tomcatHomePath = "$(TOMCAT_HOME_PATH)"

                      Write-Host "Setting CATALINA_HOME to: $tomcatHomePath"

                      # Set system environment variable
                      [Environment]::SetEnvironmentVariable("CATALINA_HOME", $tomcatHomePath, "Machine")

                      # Update PATH to include Tomcat bin directory
                      $tomcatBinPath = Join-Path $tomcatHomePath "bin"
                      $currentPath = [Environment]::GetEnvironmentVariable("PATH", "Machine")

                      # Remove any existing Tomcat paths from PATH
                      $pathEntries = $currentPath -split ";" | Where-Object { $_ -notlike "*Tomcat*" -and $_ -notlike "*catalina*" }

                      # Add new Tomcat bin path
                      $newPath = ($pathEntries + $tomcatBinPath) -join ";"
                      [Environment]::SetEnvironmentVariable("PATH", $newPath, "Machine")

                      Write-Host "Environment variables updated successfully"
                      Write-Host "CATALINA_HOME: $tomcatHomePath"
                      Write-Host "Tomcat bin added to PATH: $tomcatBinPath"

                      # Configure Tomcat to use the installed Java
                      $setenvFile = Join-Path $tomcatHomePath "bin\setenv.bat"
                      $javaHomePath = "$(JAVA_HOME_PATH)"

                      # Create setenv.bat to configure Java for Tomcat
                      $setenvLines = @(
                          "@echo off",
                          "rem Tomcat Environment Configuration",
                          "set JAVA_HOME=$javaHomePath",
                          "set JRE_HOME=%JAVA_HOME%",
                          "set CATALINA_HOME=$tomcatHomePath",
                          "",
                          "rem JVM Options",
                          "set JAVA_OPTS=-Xms512m -Xmx1024m -XX:PermSize=256m -XX:MaxPermSize=512m",
                          "set CATALINA_OPTS=-server -Djava.awt.headless=true",
                          "",
                          "echo Tomcat configured with JAVA_HOME: %JAVA_HOME%",
                          "echo CATALINA_HOME: %CATALINA_HOME%"
                      )

                      $setenvLines | Out-File -FilePath $setenvFile -Encoding ASCII
                      Write-Host "Created setenv.bat configuration file"
                    pwsh: true

                - task: PowerShell@2
                  displayName: 'Start Tomcat and Verify Installation'
                  condition: eq(variables['TomcatInstallSuccess'], 'true')
                  inputs:
                    targetType: 'inline'
                    script: |
                      # Verify the installation
                      $tomcatHomePath = "$(TOMCAT_HOME_PATH)"
                      $catalinaScript = Join-Path $tomcatHomePath "bin\catalina.bat"

                      if (Test-Path $catalinaScript) {
                          Write-Host "=== Tomcat Installation Verification ==="

                          # Display version information
                          $versionFile = Join-Path $tomcatHomePath "RELEASE-NOTES"
                          if (Test-Path $versionFile) {
                              $versionInfo = Get-Content $versionFile -First 10 | Select-String "Apache Tomcat" | Select-Object -First 1
                              Write-Host "Tomcat Version: $versionInfo"
                          }

                          Write-Host "`n=== Environment Variables ==="
                          Write-Host "CATALINA_HOME: $(TOMCAT_HOME_PATH)"
                          Write-Host "JAVA_HOME: $(JAVA_HOME_PATH)"

                          # Start Tomcat
                          Write-Host "`n=== Starting Tomcat ==="
                          $startupScript = Join-Path $tomcatHomePath "bin\startup.bat"

                          if (Test-Path $startupScript) {
                              Write-Host "Starting Tomcat server..."
                              Start-Process -FilePath $startupScript -WorkingDirectory (Join-Path $tomcatHomePath "bin") -WindowStyle Hidden

                              # Wait a moment for startup
                              Start-Sleep -Seconds 10

                              # Check if Tomcat is running
                              $tomcatProcess = Get-Process -Name "*java*" | Where-Object { $_.CommandLine -like "*catalina*" } -ErrorAction SilentlyContinue

                              if ($tomcatProcess) {
                                  Write-Host "✓ Tomcat started successfully (PID: $($tomcatProcess.Id))"

                                  # Test HTTP connection (optional)
                                  try {
                                      Start-Sleep -Seconds 5
                                      $response = Invoke-WebRequest -Uri "http://localhost:8080" -TimeoutSec 10 -ErrorAction SilentlyContinue
                                      if ($response.StatusCode -eq 200) {
                                          Write-Host "✓ Tomcat web server is responding on port 8080"
                                      }
                                  } catch {
                                      Write-Host "⚠ Tomcat is running but web interface may still be starting up"
                                  }
                              } else {
                                  Write-Warning "Tomcat process not detected - check logs for issues"
                              }
                          }

                          # Clean up old backups (keep only last 3)
                          $backupPath = "$(TOMCAT_INSTALL_PATH)\backup"
                          if (Test-Path $backupPath) {
                              $backups = Get-ChildItem -Path $backupPath -Directory | Sort-Object CreationTime -Descending
                              if ($backups.Count -gt 3) {
                                  $backupsToRemove = $backups | Select-Object -Skip 3
                                  foreach ($backup in $backupsToRemove) {
                                      Write-Host "Removing old Tomcat backup: $($backup.Name)"
                                      Remove-Item -Path $backup.FullName -Recurse -Force
                                  }
                              }
                          }

                          Write-Host "`n=== Tomcat Deployment Completed Successfully ==="
                      } else {
                          Write-Error "Tomcat installation verification failed"
                          exit 1
                      }
                    pwsh: true
